<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onLoad="init();putOnLoadDisplay();outputList();">
<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">
	<!------ システム共通ヘッダー  START ------>
	<jsp:include page="ptl000001_Header.jsp?PARAM=緊急発注登録一覧（店別）（SSN04007）"></jsp:include>
	<tr height="5"></tr>
	<!------ システム共通ヘッダー  END ------>

<!------ Body START ------>
<form name="MainForm" method="post" action="app">
<jsp:include page="rbs00000_common.jsp" />
<input type="hidden" name="Modified" value="">
<input type="hidden" name="ModifiedCondition" value="">
<input type="hidden" name="deleteLine" />
<input type="hidden" name="outPutFlg" value="<%= outPutFlg %>" />

	<tr>
		<td align="center" valign="top">

	<table border="0" cellspacing="0" cellpadding="0"   class="kensaku">
		<tr>
			<th nowrap width="80">*取引先</th>
			<td nowrap >
		        <%
				//取引先権限振分
				if(RoleUtil.isTorihikisakiFurumai(role)){
				%>
						<input type="text" name="torihikisaki_cd" value="<%= HTMLUtil.toText(emergencyItiranStatus.getTorihikisakiCd()) %>" style="width: 50px;" size="8" maxlength="<%=torihikisakiCdLen %>" id="no_input_text" tabindex="-1"readOnly>
						<input type="text" name="torihikisaki_na" value="<%= HTMLUtil.toText(emergencyItiranStatus.getTorihikisakiNa()) %>" style="width: 210px;" size="40" id="no_input_text" tabindex="-1" readOnly>
			    <%}else{%>
		                <input type="text" name="torihikisaki_cd" size="8" maxlength="<%=torihikisakiCdLen %>" value="<%= HTMLUtil.toText(emergencyItiranStatus.getTorihikisakiCd()) %>" style="ime-mode:disabled; width: 52px;" />
		                <input type="text" name="torihikisaki_na"  value="<%= HTMLUtil.toText(emergencyItiranStatus.getTorihikisakiNa()) %>" style="width: 110px;" id="no_input_text" tabindex="-1" readonly />
						<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_siireSel('MainForm.torihikisaki_cd','MainForm.torihikisaki_na');" />
						<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.torihikisaki_cd, MainForm.torihikisaki_na);" />
				<%}%>
			</td>
			<th nowrap >店舗</th>
			<td nowrap >
				<input type="text" name="tenpo_cd" value="<% if(emergencyItiranStatus.getTenpoCd() != null ) out.print(HTMLUtil.toText(emergencyItiranStatus.getTenpoCd())); %>"  size="8" maxlength="<%=tenpoCdLen %>" style="ime-mode:disabled; width: 52px;" />
				<input type="text" name="tenpo_na" value="<% if(emergencyItiranStatus.getTenpoNa() != null ) out.print(HTMLUtil.toText(emergencyItiranStatus.getTenpoNa())); %>"  id="no_input_text" tabindex="-1" readonly style="width: 110px;"  />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_tenpoSel('MainForm.tenpo_cd', 'MainForm.tenpo_na', '');"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('tenpo')"/>
			</td>
		</tr>
		<tr>
			<th nowrap >*部門</th>
			<td nowrap  >
				<input type="text" name="bunrui1_cd" value="<% if(emergencyItiranStatus.getBunrui1Cd() != null ) out.print(HTMLUtil.toText(emergencyItiranStatus.getBunrui1Cd())); %>" size="8" maxlength="<%=bunrui1CdLen %>" style="ime-mode:disabled; width: 52px;" />
				<input type="text" name="bunrui1_na" value="<% if(emergencyItiranStatus.getBunrui1Na() != null ) out.print(HTMLUtil.toText(emergencyItiranStatus.getBunrui1Na())); %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('MainForm.bunrui1_cd', 'MainForm.bunrui1_na','','','','',1,MainForm.bunrui1_cd.value,MainForm.bunrui1_na.value);"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('bunrui1')"/>
			</td>
			<th nowrap >相場区分</th>
			<td nowrap >
				<select name="soba_kb" onChange="changeModifiedCondition();">
					<option value="" <%=emergencyItiranStatus.getSobaKb().equals(SobaKb.UNKNOWN.getCode())?"selected":""%>></option>
					<option value="<%= HTMLUtil.toLabel(SobaKb.SOBA.getCode())%>"  <%=emergencyItiranStatus.getSobaKb().equals(SobaKb.SOBA)?"selected":""%> ><%= HTMLUtil.toLabel(SobaKb.SOBA.toString())%></option>


				</select>
			</td>
		</tr>
		<tr>
			<th nowrap >*納品日</th>
			<td nowrap height="22" class="string_label">
				<input type="text" name="nohin_dt_from" value="<% if(emergencyItiranStatus.getNohinDtFrom() != null ) out.print(HTMLUtil.toText(emergencyItiranStatus.getNohinDtFrom())); %>" size="10" maxlength="8" style="ime-mode:disabled; width: 62px;" />
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.nohin_dt_from);" />&nbsp;～
				<input type="text" name="nohin_dt_to" value="<% if(emergencyItiranStatus.getNohinDtTo() != null ) out.print(HTMLUtil.toText(emergencyItiranStatus.getNohinDtTo())); %>" size="10" maxlength="8" style="ime-mode:disabled; width: 62px;" />
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.nohin_dt_to);" />
				<small>（YYYYMMDD）</small>
			</td>
			<th nowrap >納品区分</th>
			<td nowrap >

				<select name="buturyu_kb" onChange="changeModifiedCondition();">
					<option value=""></option>
<%
	if( maButuryuBh.getMaxRows() > 0 ) {
		for( Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext(); ) {
			MaButuryuBean bean = (MaButuryuBean)ite.next();
			if(!HTMLUtil.toText(bean.getShiwakeKb().trim()).equals("3")) {%>
				<option value="<%= HTMLUtil.toText(bean.getButuryuKb()) %>"
				<%= emergencyItiranStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ?  " selected" : "" %>>
				<%= HTMLUtil.toLabel(bean.getButuryuNm()) %></option>
<%			}	%>
<%		}	%>
<%	}	%>
				</select>
			</td>
		</tr>
		<tr>
			<th nowrap >処理状況</th>
			<td nowrap width="200">
				<label><input type="radio" name="syori_jokyo" value="1" onClick="changeModifiedCondition();" <%= HTMLUtil.toRadioCheck(emergencyItiranStatus.getSyoriJokyo().getCode(), SyoriJokyoDictionary.SUBETE.getCode()) %> /><span><%= SyoriJokyoDictionary.SUBETE.toString() %></span></label>
				<label><input type="radio" name="syori_jokyo" value="2" onClick="changeModifiedCondition();" <%= HTMLUtil.toRadioCheck(emergencyItiranStatus.getSyoriJokyo().getCode(), SyoriJokyoDictionary.MIKAKUTEI.getCode()) %> /><span><%= SyoriJokyoDictionary.MIKAKUTEI.toString() %></span></label>
				<label><input type="radio" name="syori_jokyo" value="3" onClick="changeModifiedCondition();" <%= HTMLUtil.toRadioCheck(emergencyItiranStatus.getSyoriJokyo().getCode(), SyoriJokyoDictionary.KAKUTEI.getCode()) %> /><span><%= SyoriJokyoDictionary.KAKUTEI.toString() %></span></label>
			</td>
			<th nowrap >経由センタ</th>
			<td nowrap>
				<input type="text" name="center_cd" value="<% if(emergencyItiranStatus.getCenterCd() != null ) out.print(HTMLUtil.toText(emergencyItiranStatus.getCenterCd())); %>" size="<%= EmergencyItiranStatus.CENTER_CD_LENGTH %>" maxlength="<%=centerCdLen %>"  style="ime-mode:disabled; width: <%= EmergencyItiranStatus.CENTER_CD_LENGTH*5 + 12  %>px;" />
				<input type="text" name="center_na" value="<% if(emergencyItiranStatus.getCenterNa() != null ) out.print(HTMLUtil.toText(emergencyItiranStatus.getCenterNa())); %>" size="15" style="width: 85px;" id="no_input_text" tabindex="-1" readonly />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_centerSel('MainForm.center_cd', 'MainForm.center_na');"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('center')"/>
			</td>
		</tr>
	</table>



	<br />

	<div>
	<input type="button" value="&emsp;検&emsp;索&emsp;" style="width: 97px;" class="btn" onClick="doSearchTran();" />
	<input type="button" value="&emsp;追&emsp;加&emsp;" style="width: 97px;" class="btn" onClick="doAddTran();" />
	<input type="button" value="&emsp;戻&emsp;る&emsp;" style="width: 92px;" class="btn" onClick="doBackTran();" /><br />
	</div>

	<br />

	<jsp:include page="InfoStringMdWare.jsp" />

	<br />

<%	if( emergencyItiranBh.getMaxRows() > 0 ) {	%>

	<table border="0" cellspacing="0" cellpadding="2">
		<tr>
			<td class="size12" height="14" align="left" style="padding: 2px;">選択チェックボックスを
				<input type="button" name="btn_all" 	style="width: 112px; height: 21px;" value="&emsp;全て選択&emsp;" 		class="select_btn" onclick ="setCheckData()"/>
				<input type="button" name="btn_none" 	style="width: 112px; height: 21px;" value="全て選択解除" 				class="select_btn" onclick="notSetCheckData()"/>
				<input type="button" name="mikaku_all" 	style="width: 116px; height: 21px;" value="未確定&emsp;選択" 			class="select_btn" onclick ="setMikakuCheckData()"/>
				<input type="button" name="kakutei_all" style="width: 109px; height: 21px;" value="&nbsp;確定&emsp;選択&nbsp;" 	class="select_btn" onclick="setKakuteiCheckData()"/>
				<input type="button" name="d_list_all" 	style="width: 176px; height: 21px;" value="納品リスト出力済選択" 		class="select_btn" onclick="setDListCheckData()"/>
			</td>
		</tr>
	</table>

	<table>
	<tr align = "left">
	<td>
		<div id="dataHeader" align="center" style="overflow:hidden;width: 970px;">
			<div align="left" style="overflow-x:hidden;height: 30px;width: 1382px;">
			<table class="data" cellSpacing="1" cellPadding="0" border="0" style="line-height: 14px;width:1399px;">
				<TR>
					<th nowrap width="20" align="center">選択</th>
					<th nowrap width="60" align="center">納品</th>
					<th nowrap width="79" align="center">伝票番号</th>
					<th nowrap width="55" align="center">部門<br>コード</th>
					<th nowrap width="140" align="center">部門名</th>
					<th nowrap width="60" align="center">店舗<br>コード
					<th nowrap width="140" align="center">店舗名</th>
					<th nowrap width="80" align="center">相場区分</th>
					<th nowrap width="80" align="center">納品区分</th>
					<th nowrap width="34" align="center">便</th>
					<th nowrap width="85" align="center">納品日</th>
				    <th nowrap width="42" align="center"></th>
					<th nowrap width="42" align="center"></th>
					<th nowrap width="225" align="center">登録者</th>
					<th nowrap width="225" align="center">確定者</th>
					<th nowrap width="15" align="center"></th>
				</TR>
			</table>
			</div>
		</div>

		<div align="center" style="overflow:scroll;width: 987px;height: 170px;"onscroll="document.all.dataHeader.scrollLeft=this.scrollLeft;">
			<div  align="left" style="overflow:hidden;height: auto;width: 1383px;">
			<table border="0" cellspacing="1" cellpadding="0" class="data"  >
				<%
				Iterator iteemergencyItiranBH = emergencyItiranBh.getBeanIterator();
				DtHachuBean emergencyItiranBean = null;
				for( int i = 0; iteemergencyItiranBH.hasNext(); i++ )
				{
				emergencyItiranBean = (DtHachuBean)iteemergencyItiranBH.next();
				%>
		        <TR>
					<%-- 選択 --%>
					<td nowrap width="20" align="center" height="18">
						<input type="checkbox" name="sentaku<%= i %>" value="1" onClick="changeModified();" />
					</td>

					<%-- 納品処理区分 --%>
					<td nowrap width="60" align="center">
						<input type="hidden" name="nohin_syori_kb_<%= i %>" value="<%= HTMLUtil.toText(emergencyItiranBean.getNohinSyoriKb()) %>" />
						<%= HTMLUtil.toLabel(NohinSyoriKbShort.getStatus(emergencyItiranBean.getNohinSyoriKb()).toString()) %>
					</td>

					<%-- 伝票番号 --%>
					<td nowrap width="75" align="center" class="string_label">
						<a href="#" onClick="emergencyTorokuCommand(<%= i %>);">
					    <%= HTMLUtil.toLabel(emergencyItiranBean.getDenpyoNb()) %>
					    </a>
					</td>

					<%-- 部門 --%>
					<td nowrap width="55" align="center" >
						<%= HTMLUtil.toLabel(emergencyItiranBean.getBunrui1Cd()) %>
					</td>

					<%-- 部門名 --%>
					<td nowrap width="140" align="center" >
						<INPUT readOnly size=23 style="border-width: 0px; width: 125px;" value="<%= HTMLUtil.toLabel(emergencyItiranBean.getBunrui1Na()) %>" tabIndex ="-1">
					</td>

					<%-- 店舗コード --%>
					<td nowrap width="60" align="center">
						<%= emergencyItiranBean.isIkkatuSiireDenpFg() ? HTMLUtil.toLabel(emergencyItiranBean.getIdoTenpoCd()) : HTMLUtil.toLabel(emergencyItiranBean.getTenpoCd()) %>
					</td>

					<%-- 店舗名 --%>
					<td nowrap width="140" align="center" >
						<INPUT readOnly size=23 style="border-width: 0px; width: 125px;" value="<%= emergencyItiranBean.isIkkatuSiireDenpFg() ? HTMLUtil.toLabel(emergencyItiranBean.getIdoTenpoNa()) : HTMLUtil.toLabel(emergencyItiranBean.getTenpoNa()) %>" tabIndex ="-1">
					</td>

					<%-- 相場 --%>
					<td nowrap width="80" align="center" >
						<%= HTMLUtil.toLabel(SobaKb.getStatus(emergencyItiranBean.getKbSobaKb()).toString()) %>
					</td>

					<%-- 物流 --%>
					<td nowrap width="76" align="center"  class="string_label">
						<%= HTMLUtil.toLabel(emergencyItiranBean.getButuryuNm()) %>
					</td>

					<%-- 便 --%>
					<td nowrap width="30" align="right"  class="numeric_label">
						<%= HTMLUtil.toLabel(emergencyItiranBean.getBinNmString()) %>
					</td>

					<%-- 納品日 --%>
					<td nowrap width="85" align="center" >
						<%= HTMLUtil.toDate(emergencyItiranBean.getNohinDt(), "yyyy/MM/dd") %>
					</td>

					<%-- 削除ボタン --%>
					<td nowrap width="42" align="center"  >
						<%	if(RoleUtil.isTorihikisakiFurumai(role)) {	%>
						    <%	if(emergencyItiranBean.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
						    		|| emergencyItiranBean.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
						    		|| emergencyItiranBean.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
						    		|| emergencyItiranBean.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
						        <%	if( emergencyItiranBean.getNohinSyoriKb().equals(NohinSyoriKb.JYUCHU_MI.getCode()) ) {	%>
							        <img src="./images/b_delete_w.png" width="40	" height="20" align="absmiddle" alt="削除" onClick="javascript:doDeleteTran('<%=i%>')"/>
								<%	}	%>
							<%	}	%>
						<%	} else { %>
								<%	if( emergencyItiranBean.getNohinSyoriKb().equals(NohinSyoriKb.JYUCHU_MI.getCode()) ) {	%>
							        	<img src="./images/b_delete_w.png" width="40	" height="20" align="absmiddle" alt="削除" onClick="javascript:doDeleteTran('<%=i%>')"/>
								<%	}	%>
						<%	}	%>
					</td>
					<td nowrap width="42" align="center" >
						<%	if(RoleUtil.isTorihikisakiFurumai(role)) {	%>
						    <%	if(emergencyItiranBean.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
						    		|| emergencyItiranBean.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
						    		|| emergencyItiranBean.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
						    		|| emergencyItiranBean.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
						        <img src="./images/b_copy_w.gif" width="40" height="20" align="absmiddle" alt="コピー" onClick="javascript:doCopyTran('<%=i%>')"/>
							<%	}	%>
						<%	} else { %>
								<img src="./images/b_copy_w.gif" width="40" height="20" align="absmiddle" alt="コピー" onClick="javascript:doCopyTran('<%=i%>')"/>
						<%	}	%>
					</td>

					<%-- 登録者 --%>
					<td nowrap width="225" align="center" >
						<INPUT readOnly size=40 style="border-width: 0px; width: 210px;" value="<%= HTMLUtil.toLabel(emergencyItiranBean.getInsertUserId().trim() + ' ' + emergencyItiranBean.getInsertUserNa()) %>" tabIndex ="-1">
					</td>
					<%-- 確定者 --%>
					<td nowrap width="225" align="center" >
						<INPUT readOnly size=40 style="border-width: 0px; width: 210px;" value="<%= HTMLUtil.toLabel(emergencyItiranBean.getJuchuKakuteiUserId().trim() + ' ' + emergencyItiranBean.getJuchuKakuteiUserNa()) %>" tabIndex ="-1">
					</td>
				</TR>
				<%
				}
				%>
			</table>
			</div>
		</div>

		<DIV align="center" style="height: 14px;">
			<font color="#0000FF">
				<B>納品</B>：未確定＝緊急発注データ 未確定&emsp;確定＝緊急発注データ　確定済&emsp;出力済＝ 納品リスト出力済
			</font>
		</DIV>
		<br/>
	</td>
	</tr>
	</table>




	<table border="0" cellspacing="0" cellpadding="0" style="height: 35px;">
		<td align="center">
			<div class="navi_data">
				<%
				String first = "";
				String prev = "";
				String next = "";
				String last = "";

				if( emergencyItiranBh.getCurrentPageNumber() == 1 ) {
					first = "disabled";
					prev = "disabled";
				}
				if( emergencyItiranBh.getCurrentPageNumber() == emergencyItiranBh.getLastPageNumber() || emergencyItiranBh.getLastPageNumber() == 0) {
					next = "disabled";
					last = "disabled";
				}
				%>
				<input type="button" value="先頭" <%= first %> onClick="changePage('first');" style="width: 38px; height: 21px;" />
				<input type="button" value="前の<%= emergencyItiranStatus.LOWS_IN_PAGE %>件" <%= prev %> onClick="changePage('prev');" style="width: 76px; height: 21px;" />
				<input type="button" value="次の<%= emergencyItiranStatus.LOWS_IN_PAGE %>件" <%= next %> onClick="changePage('next');" style="width: 76px; height: 21px;" />
				<input type="button" value="最終" <%= last %> onClick="changePage('last');" style="width: 38px; height: 21px;" />
			</div>
			<span class="size12">(<%= emergencyItiranBh.getMaxRows() %>件中<%= emergencyItiranBh.getStartRowInPage() %>～<%= emergencyItiranBh.getEndRowInPage() %>件目）</span>
		</td>
	</table>

	<input type="button" name="" value="&emsp;確&emsp;定&emsp;" class="btn" onClick="decideCommand();" style="width: 97px;"/>
	<input type="button" name="" value="納品リスト出力" class="btn" onClick="deliveryListCommand(this.form);" style="width: 118px;"/>

<%
}
%>

</form>
<!-- Body END -->
<!---- システム共通フッター START ---->
	<table border="2" cellspacing="0" cellpadding="0" class="kensaku tableNote" width="500">
		<tr>
			<td>
			<br>
            &emsp;<font color="#0000FF"><b>※検索について</b><br/>
			&emsp;　納品日（自）のみ指定されている場合は、指定された納品日のみが対象となります。<br />
			&emsp;&emsp;</font>
            <br>
			<br />
			</td>
			<td style="width: 3px; border-width: 0px;"></td>
		</tr>
		<tr style="height: 1px;"></tr>
	</table>
<!---- システム共通フッター END ---->
</table>
</body>