<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onLoad="init();putOnLoadDisplay();">
<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">
<!------ システム共通ヘッダー  START ------>
	<jsp:include page="ptl000001_Header.jsp?PARAM=商品情報コメント一覧（SSN02005）"></jsp:include>
   	<tr height="5"></tr>    		
<!------ システム共通ヘッダー  END ------>
<!------ Body START ------>
 <tr>
   	<td align="center" valign="top">
	<form name="MainForm" method="post" action="app">
	<input type="hidden" name="ModifiedCondition" value=""/><%-- 検索条件の変更フラグ --%>
	<input type="hidden" name="Modified" value=""/><%-- 明細更新フラグ --%>
	<input type="hidden" name="userKb" value="<%=userKb%>">
	<input type="hidden" name="line" value=""/>
		<jsp:include page="rbs00000_common.jsp" flush="true" />
		<table border="0" cellspacing="1" cellpadding="0" class="kensaku">
		<tr>
			<th nowrap>*取引先</th>
			<td nowrap width="350">
			<%
			//取引先権限振分
			Set role = ((UserBean)userSession.getBeanList().get(0)).getRoles();
			if(RoleUtil.isTorihikisakiFurumai(role)){
			%>
	    	        <input type="text" name="torihikisaki_cd" size="8" style="width: 52px;" value="<%=HTMLUtil.toText(dataHolder.getParameter("torihikisaki_cd"))%>" id="no_input_text" tabindex="-1" readonly />
		            <input type="text" name="torihikisaki_na" size="30" style="width: 110px;" value="<%=HTMLUtil.toText(dataHolder.getParameter("torihikisaki_na"))%>" id="no_input_text" tabindex="-1" readonly />
		    <%}else{%>
	                <input type="text" name="torihikisaki_cd" size="8" style="width: 52px; ime-mode:disabled;" maxlength="<%=torihikisakiCdLen %>" value="<%=HTMLUtil.toText(dataHolder.getParameter("torihikisaki_cd"))%>" />
	                <input type="text" name="torihikisaki_na" size="20" style="width: 110px;" value="<%=HTMLUtil.toText(dataHolder.getParameter("torihikisaki_na"))%>" id="no_input_text" tabindex="-1" readonly />
	                <img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_siireSel('MainForm.torihikisaki_cd', 'MainForm.torihikisaki_na');" />
	                <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.torihikisaki_cd, MainForm.torihikisaki_na);" />
			<%}%>
			</td>
			<th nowrap>*部門</th>
			<td nowrap width="350">	
				<input type="text" name="bunrui1_cd" size="2" style="width: 22px; ime-mode:disabled;" maxlength="<%=bunrui1CdLen %>" value ="<%=HTMLUtil.toText(dataHolder.getParameter("bunrui1_cd"))%>">
				<input type="text" name="bunrui1_na" size="20" style="width: 110px;" value="<%=HTMLUtil.toText(dataHolder.getParameter("bunrui1_na"))%>" id="no_input_text" tabindex="-1" readonly />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('MainForm.bunrui1_cd' ,'MainForm.bunrui1_na','','','','','1', MainForm.bunrui1_cd.value ,'');"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.bunrui1_cd, MainForm.bunrui1_na);"/>
			</td>
		</tr>
		<tr>
			<th nowrap>商品</th>
			<td nowrap>
				<input type="text" name="syohin_cd" style="width: 97px; ime-mode:disabled;" value="<%=HTMLUtil.toText(dataHolder.getParameter("syohin_cd"))%>" maxlength="<%=syohinCdLen %>" size="17" />
				<input type="text" name="syohin_na" style="width: 135px;" value="<%=HTMLUtil.toText(dataHolder.getParameter("syohin_na"))%>" size="25" id="no_input_text" tabindex="-1" readonly />
				<img src="images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"  onClick="pop_syohinSel('MainForm.syohin_cd','MainForm.syohin_na', '' , MainForm.bunrui1_cd ,'' , '' ,'<%=SobaKb.SOBA.getCode()%>' ,'' ,'');" />
				<img src="images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clearText('syohin_cd','syohin_na')"/>
			</td>
		</tr>
		</table>
		<br>
		<input type="button" name="commitBtn" value="&emsp;検&emsp;索&emsp;"class="btn" onclick="goSearch()">
		<input type="button" name="commitBtn" value="&emsp;戻&emsp;る&emsp;"class="btn" onclick="goBack()">
		
		<br>
		<br>
		<jsp:include page="InfoStringMdWare.jsp" />
		<br>
		<br>

		<% if ( commentList != null && commentList.getBeanList() != null && commentList.getBeanList().size() > 0 ) {
		   List comment = commentList.getBeanList();
		%>		
<table>
<tr align = "left">
<td>
	<div align="left" style="overflow-x:hidden; width: 980px; height: 30px;">
	<table border="0" cellspacing="1" cellpadding="0" class="data">
		<tr style="height: 28px;">
			<th nowrap width="40" align="center"></th>
			<th nowrap width="108" align="center">商品コード</th>
			<th nowrap width="125" align="center">商品名</th>
			<th nowrap width="70" align="center">産地</th>
			<th nowrap width="52" align="center">等級</th>
			<th nowrap width="70" align="center">規格</th>
			<th nowrap width="220" align="center">コメント<br>(先頭２０文字程度)</th>
			<th nowrap width="78" align="center">掲載開始日</th>
			<th nowrap width="78" align="center">掲載終了日</th>
			<th nowrap width="70" align="center">登録者</th>
			<th nowrap width="40" align="center"></th>
			<th nowrap width="15" align="center"></th>
		</tr>
	</table>
	</div>

	<div  align="left" style="overflow:scroll;overflow-x:hidden; width: 980px; height: 265px;">
	<table border="0" cellspacing="1" cellpadding="0" class="data"  >		
		<% 	   	
		for(int i = 0; i < comment.size(); i++){
	   		SyohinCommentBean bean = (SyohinCommentBean)comment.get(i);
		%>
		<tr>		
			<%-- <td width="40">
				<img src="images/b_delete_w.png" width="40" height="20" align="absmiddle" alt="削除" onClick="goDelete('<%=i%>')"/> --%>
			<%-- </td> --%>
			<td width="40"><input style="width: 38px; height: 21px;" type="button" value="変更" onClick="goRegComment('<%=i%>')"></td>
			<%-- 商品コードor発注商品コード --%>
			<td nowrap width="108" align="center" ><INPUT readOnly size=17 style="width: 95px; border-width: 0px;" value="<%=HTMLUtil.toText(bean.getShowSyohinCd())%>" tabindex="-1"></td>	
			<%-- 商品名 --%>
			<td nowrap width="125" align="center" ><INPUT readOnly size=20 style="width: 110px; border-width: 0px;" value="<%=StringUtility.ztrim(HTMLUtil.toText(bean.getSyohinNa()))%>" tabindex="-1"></td>
			<%-- 産地名 --%>
			<td nowrap width="70" align="center"><INPUT readOnly size=10 style="width: 60px; border-width: 0px;" value="<%=HTMLUtil.toText(bean.getSantiNa())%>" tabindex="-1"></td>
			<%-- 等階級名 --%>
			<td nowrap width="52" align="center"><INPUT readOnly size=8 style="width: 50px; border-width: 0px;" value="<%=HTMLUtil.toText(bean.getTokaikyuNa())%>" tabindex="-1"></td>
			<%-- 規格名 --%>
			<td nowrap width="70" align="center"><INPUT readOnly size=10 style="width: 60px; border-width: 0px;" value="<%=HTMLUtil.toText(bean.getKikakuNa())%>" tabindex="-1"></td>
			<%-- コメント--%>
			<td nowrap width="220" align="center"><INPUT readOnly size=40 style="width: 210px; border-width: 0px;" value="<%=HTMLUtil.toText(bean.getComment())%>" tabindex="-1"></td>
			<%-- 掲載開始日 --%>
			<td nowrap width="78" align="center"><%=DateChanger.toSeparatorDate(bean.getKeisaiFmDt())%></td>
			<%-- 掲載終了日 --%>
			<td nowrap width="78" align="center"	><%=DateChanger.toSeparatorDate(bean.getKeisaiToDt())%></td>
			<%-- 登録者 --%>
			<td nowrap width="70" align="center" ><INPUT readOnly size=10 style="width: 60px; border-width: 0px;" value="<%=HTMLUtil.toText(bean.getRiyoUserNa())%>" tabindex="-1"></td>

			<td width="40">
				<img src="images/b_delete_w.png" width="40" height="20" align="absmiddle" alt="削除" onClick="goDelete('<%=i%>')"/>
			</td>
		</tr>
		<%}%>
	</table>
	</div>
</td>
</tr>
</table>	

		<br>
		<table width="90%" border="0" cellspacing="0" cellpadding="0">
		<tr>
		<td align = "center" nowrap>
<%
		String first = "";
		String before = "";
		String next = "";
		String last = "";

		if ( commentList.getCurrentPageNumber() == 1 ) {
			first = "disabled";
			before = "disabled";
		}
		if ( commentList.getCurrentPageNumber() == commentList.getLastPageNumber() || commentList.getLastPageNumber() == 0) {
			next = "disabled";
			last = "disabled";
		}
%>
		<input type="button" style="width: 38px; height: 21px;" value="先頭" <%= first %> onClick="javaScript:changePage('first');">
		<input type="button" style="width: 86px; height: 21px;" value="前の100件" <%= before %> onClick="javaScript:changePage('before');">
		<input type="button" style="width: 86px; height: 21px;" value="次の100件" <%= next %> onClick="javaScript:changePage('next');">
		<input type="button" style="width: 38px; height: 21px;" value="最終" <%= last %> onClick="javaScript:changePage('last');">
		<br>
	    (<%=commentList.getMaxRows()%>件中<%=commentList.getStartRowInPage()%>～<%=commentList.getEndRowInPage()%>件目）
	    </div>
		</td>
		</tr>
		</table>
		<%}%>
	</form>
	</td>
</tr>
<!---- Body END ---->

<!---- システム共通フッター START ---->
<!---- システム共通フッター END ---->
</table>
</body>