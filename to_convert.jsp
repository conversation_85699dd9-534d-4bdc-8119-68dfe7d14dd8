<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onLoad="init();putOnLoadDisplay();outputList();">
<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">
	<!------ システム共通ヘッダー  START ------>
	<jsp:include page="ptl000001_Header.jsp?PARAM=緊急発注登録一覧（商品店別）（SSN04009）"></jsp:include>
	<tr height="5"></tr>
	<!------ システム共通ヘッダー  END ------>
<!------ Body START ------>
<form name="MainForm" method="post" action="app">
<jsp:include page="rbs00000_common.jsp" />
<input type="hidden" name="Modified" value="">
<input type="hidden" name="ModifiedCondition" value="">
<input type="hidden" name="deleteLine" />
<input type="hidden" name="outPutFlg" value="<%= outPutFlg %>" />

	<tr>
		<td align="center" valign="top">

		<table border="0" cellspacing="0" cellpadding="0"  class="kensaku">
		<tr>
			<th nowrap >*取引先</th>
			<td nowrap  width="300">
		        <%
				//取引先権限振分
				if(RoleUtil.isTorihikisakiFurumai(role)){
				%>
						<input type="text" name="torihikisaki_cd" value="<%= HTMLUtil.toText(emergencySyohinItiranStatus.getTorihikisakiCd()) %>" style="width: 50px;" size="8" maxlength="<%=torihikisakiCdLen %>" id="no_input_text" tabindex="-1"readOnly>
						<input type="text" name="torihikisaki_na" value="<%= HTMLUtil.toText(emergencySyohinItiranStatus.getTorihikisakiNa()) %>" style="width: 210px;" size="40" id="no_input_text" tabindex="-1" readOnly>
			    <%}else{%>
		                <input type="text" name="torihikisaki_cd" size="8" maxlength="<%=torihikisakiCdLen %>" value="<%= HTMLUtil.toText(emergencySyohinItiranStatus.getTorihikisakiCd()) %>" style="ime-mode:disabled; width: 52px;" />
		                <input type="text" name="torihikisaki_na"  value="<%= HTMLUtil.toText(emergencySyohinItiranStatus.getTorihikisakiNa()) %>" style="width: 110px;" id="no_input_text" tabindex="-1" readonly />
						<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_siireSel('MainForm.torihikisaki_cd','MainForm.torihikisaki_na');" />
						<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.torihikisaki_cd, MainForm.torihikisaki_na);" />
				<%}%>
			</td>
			<th nowrap >*納品日</th>

			<td nowrap height="22" class="string_label" colspan="3">
				<input type="text" <%=nohin_dt_from%> size="15" maxlength="8" style="ime-mode:disabled; width: 87px;" />
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.nohin_dt_from);" />(自)&nbsp;～
				<input type="text" <%=nohin_dt_to%> size="15" maxlength="8" style="ime-mode:disabled; width: 87px;" />
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.nohin_dt_to);" />(至)
				<small>（YYYYMMDD）</small>
			</td>
		</tr>
		<tr>
			<th nowrap >*部門</th>
			<td nowrap  >
				<input type="text" size="3" <%=bunrui1_cd%> style="ime-mode:disabled; width: 27px;" />
				<input type="text" size="31" <%=bunrui1_na%> style="width: 165px;" id="no_input_text" tabindex="-1" readonly />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('MainForm.bunrui1_cd', 'MainForm.bunrui1_na','','','','',1,MainForm.bunrui1_cd.value,MainForm.bunrui1_na.value);"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('bunrui1')"/>
			</td>
			<th nowrap >相場区分</th>
			<td nowrap width="130">
				<select name="soba_kb" onChange="changeModifiedCondition();">
					<option value=""></option>
					<option value="<%= HTMLUtil.toLabel(SobaKb.SOBA.getCode())%>"  <%=emergencySyohinItiranStatus.getSobaKb().equals(SobaKb.SOBA)?"selected":""%> ><%= HTMLUtil.toLabel(SobaKb.SOBA.toString())%></option>
				</select>
			</td>
			<th nowrap style="width: 84px;" >納品区分</th>
			<td nowrap width="130">
				<select name="buturyu_kb" onChange="changeModifiedCondition();">
					<option value=""></option>
<%
	if( maButuryuBh.getMaxRows() > 0 ) {
		for( Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext(); ) {
			MaButuryuBean bean = (MaButuryuBean)ite.next();
			if(!HTMLUtil.toText(bean.getShiwakeKb().trim()).equals("3")) {%>
				<option value="<%= HTMLUtil.toText(bean.getButuryuKb()) %>"
				<%= emergencySyohinItiranStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ?  " selected" : "" %>>
				<%= HTMLUtil.toLabel(bean.getButuryuNm()) %></option>
<%			}	%>
<%		}	%>
<%	}	%>
				</select>
			</td>
		</tr>
		<tr>
			<th nowrap >*処理状況</th>
			<td nowrap >
				<label><input type="radio" name="syori_jokyo"  value="<%= HTMLUtil.toLabel(SyoriJokyoDictionary.SUBETE.getCode())%>"  <%=emergencySyohinItiranStatus.getSyoriJokyo().equals(SyoriJokyoDictionary.SUBETE)?"checked":""%> onClick="changeModifiedCondition();" /><span><%= HTMLUtil.toLabel(SyoriJokyoDictionary.SUBETE.toString())%></span></label>
				<label><input type="radio" name="syori_jokyo"  value="<%= HTMLUtil.toLabel(SyoriJokyoDictionary.MIKAKUTEI.getCode())%>"  <%=emergencySyohinItiranStatus.getSyoriJokyo().equals(SyoriJokyoDictionary.MIKAKUTEI)?"checked":""%> onClick="changeModifiedCondition();" /><span><%= HTMLUtil.toLabel(SyoriJokyoDictionary.MIKAKUTEI.toString())%></span></label>
				<label><input type="radio" name="syori_jokyo"  value="<%= HTMLUtil.toLabel(SyoriJokyoDictionary.KAKUTEI.getCode())%>"  <%=emergencySyohinItiranStatus.getSyoriJokyo().equals(SyoriJokyoDictionary.KAKUTEI)?"checked":""%> onClick="changeModifiedCondition();" /><span><%= HTMLUtil.toLabel(SyoriJokyoDictionary.KAKUTEI.toString())%></span></label>
			</td>
			<th>経由センタ</th>
			<td nowrap colspan="3">
				<input type="text" <%=center_cd%> style="ime-mode:disabled; width: 32px;" />
				<input type="text" <%=center_na%> id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_centerSel('MainForm.center_cd', 'MainForm.center_na');" />
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('center');" />
			</td>
		</tr>
	</table>



	<br />

	<input type="button" value="&emsp;検&emsp;索&emsp;" class="btn" onClick="doSearchTran();" style="width: 97px;" />
	<input type="button" value="&emsp;追&emsp;加&emsp;" class="btn" onClick="doAddTran();" style="width: 97px;" />
	<input type="button" value="&emsp;戻&emsp;る&emsp;" class="btn" onClick="doBackTran();" style="width: 92px;" /><br />

	<br />

<CENTER>
		<table width="100%" border="0" cellspacing="0" cellpadding="2" class="guide">
<tr><td align="center">

		<jsp:include page="InfoStringMdWare.jsp" />


</td></tr>
</table>
</CENTER>

	<br />

<%	if( emergencySyohinItiranBh.getMaxRows() > 0 ) {	%>


	<table border="0" cellspacing="0" cellpadding="2">
		<tr>
			<td class="size12" height="14" align="left" style="padding: 2px;">選択チェックボックスを
				<input type="button" style="width: 112px; height: 21px;" name="btn_all" 	value="&emsp;全て選択&emsp;" 		class="select_btn" onclick ="setCheckData()"/>
				<input type="button" style="width: 112px; height: 21px;" name="btn_none" 	value="全て選択解除" 				class="select_btn" onclick="notSetCheckData()"/>
				<input type="button" style="width: 116px; height: 21px;" name="mikaku_all" 	value="未確定&emsp;選択" 			class="select_btn" onclick ="setMikakuCheckData()"/>
				<input type="button" style="width: 109px; height: 21px;" name="kakutei_all" value="&nbsp;確定&emsp;選択&nbsp;" 	class="select_btn" onclick="setKakuteiCheckData()"/>
				<input type="button" style="width: 103px; height: 21px;" name="d_list_all" 	value="出力済&nbsp;選択" 			class="select_btn" onclick="setDListCheckData()"/>
			</td>
		</tr>
	</table>

<table>
<tr align = "left">
<td>
	<div id="dataHeader" align="center" style="overflow:hidden;width: 970px;">
		<div align="left" style="overflow-x:hidden; width: 1428px; height: 30px;">
		<table border="0" cellspacing="1" cellpadding="0" class="data" style="line-height: 14px; width:1428px;">
			<tr>
				<th nowrap width="25" align="center">選択</th>
				<th nowrap width="40" align="center">納品</th>
				<th nowrap width="104" align="center">商品コード</th>
				<th nowrap width="110" align="center">商品名</th>
				<th nowrap width="28" align="center">入</br>数</th>
				<th nowrap width="42" align="center">発注<br />単位</th>
				<th nowrap width="38" align="center">登録<br />数</th>
				<th nowrap width="40" align="center">登録<br />数量</th>
				<th nowrap width="45" align="center">原単価</th>
				<%// 取引先は非表示
				if (RoleUtil.isTorihikisakiFurumai(role)) { %>
					<th nowrap width="40" align="center"></th>
				<%} else { %>
					<th nowrap width="40" align="center">出庫<br />単価</th>
				<%} %>
				<th nowrap width="45" align="center">売単価</th>
				<th nowrap width="38" align="center">値入<br />率</th>
				<th nowrap width="45" align="center">産地</th>
				<th nowrap width="40" align="center">等級</th>
				<th nowrap width="40" align="center">規格</th>
				<th nowrap width="55" align="center">納品<br />区分</th>
				<th nowrap width="20" align="center">便</th>
				<th nowrap width="80" align="center">納品日</th>
			    <th nowrap width="40"></th>
			    <th nowrap width="40"></th>
			    <th nowrap width="225" align="center">登録者</th>
				<th nowrap width="225" align="center">確定者</th>
			</tr>
		</table>
		</div>
	</div>

	<div align="center" style="overflow:scroll;width: 987px;height: 169px;" onscroll="document.all.dataHeader.scrollLeft=this.scrollLeft;">
		<div  align="left" style="overflow:hidden; width: 1428px; height: auto;">
			<table border="0" cellspacing="1" cellpadding="0" class="data" style="width:1428px"  >

	<%
		Iterator it = emergencySyohinItiranBh.getBeanIterator();
		for( int i = 0; it.hasNext(); i++ ){
			DtKinkyuSyohinHachuItiranSearchBean itiranBean = (DtKinkyuSyohinHachuItiranSearchBean)it.next();
	%>

			<tr>
				<!------ 選択 ------>
				<td nowrap width="25" align="center"><input type="checkbox" name="sentaku<%=i%>" value="0" /></td>

				<!------ 納品 ------>
				<td nowrap align="center" width="40">
					<input type="hidden" name="nohin_syori_kb<%=i%>" value="<%=itiranBean.getNohinSyoriKb().getCode()%>" />
					<%=itiranBean.getNohinSyoriKb().toString()%>
				</td>
				<!------ 商品コード ------>
				<td nowrap  width="104" align="center"><a href="#" onClick="emergencyTorokuCommand(<%=i%>);">
					<input type="hidden" name="syohin_cd<%=i%>" value="<%=HTMLUtil.toText(itiranBean.getSyohinCd())%>"/>
					<%=HTMLUtil.toText(itiranBean.getSyohinCd())%></a>
				</td>

				<!------ 商品名 ------>
				<td nowrap align="center" width="110">
					<INPUT type="text" size=19 style="border-width: 0px; width: 105px;" value="<%=HTMLUtil.toText(StringUtility.trim(itiranBean.getSyohinNa().trim()))%>" tabindex="-1" readonly>
				</td>

				<!------ 入数 ------>
				<td nowrap align="center" width="28">
					<INPUT type="text" size=2 style="border-width: 0px; width: 20px; text-align:right;" value="<%=HTMLUtil.toText(itiranBean.getIrisuQt())%>" tabindex="-1" readonly>
				</td>

				<!------ 発注単位 ------>
				<td nowrap  align="center" width="42">
					<INPUT type="text" size=4 style="border-width: 0px; width: 30px;" value="<%=HTMLUtil.toText(itiranBean.getHachuTaniNa())%>" tabindex="-1" readonly>
				</td>

				<!------ 登録数 ------>
				<td nowrap align="center" width="38">
					<INPUT type="text" size=4 style="border-width: 0px; width: 30px; text-align:right;" value="<%=HTMLUtil.toText(itiranBean.getHachuQt())%>" tabindex="-1" readonly>
				</td>

				<!------ 登録数量 ------>
				<td nowrap align="center" width="40">
					<INPUT type="text" size=5 style="border-width: 0px; width: 35px; text-align:right;" value="<%=HTMLUtil.toLabel(HTMLUtil.toText(itiranBean.getHachuSuryoQt()),"0.##")%>" tabindex="-1" readonly>
				</td>

				<!------ 原単価 ------>
				<td nowrap align="center" width="45" >
					<INPUT type="text" size=6 style="border-width: 0px; width: 40px; text-align:right;" value="<%=HTMLUtil.toLabel(HTMLUtil.toText(itiranBean.getMinGentankaVl()), "#,###,##0.00")%><%=(itiranBean.getMinGentankaVl()==itiranBean.getMaxGentankaVl())?"":"*"%>" tabindex="-1" readonly>
				</td>

				<!------ 出庫単価 ------>
				<td nowrap align="center" width="40">
					<%
					// 取引先は非表示
					if(RoleUtil.isTorihikisakiFurumai(role)){
					%>
						&emsp;
			    	<%}else{%>
						<INPUT type="text" size=5 style="border-width: 0px; width: 35px; text-align:right;" value="<%=HTMLUtil.toLabel(HTMLUtil.toText(itiranBean.getMinIdoGentankaVl()), "#,###,##0.00")%><%=(itiranBean.getMinIdoGentankaVl()==itiranBean.getMaxIdoGentankaVl())?"":"*"%>" tabindex="-1" readonly>
					<%}%>
				</td>

				<!------ 売単価 ------>
				<td nowrap align="center" width="45" >
					<INPUT type="text" size=6 style="border-width: 0px; width: 40px; text-align:right;" value="<%=HTMLUtil.toLabel(HTMLUtil.toText(itiranBean.getMinBaitankaVl()), "#,###,##0")%><%=(itiranBean.getMinBaitankaVl()==itiranBean.getMaxBaitankaVl())?"":"*"%>" tabindex="-1" readonly>
				</td>

				<!------ 値入率 ------>
				<td nowrap align="center" width="38" >
					<INPUT type="text" size=5 style="border-width: 0px; width: 35px; text-align:right;" value="<%=CalcUtil.calcNeiriRitu(Double.toString(itiranBean.getMinGentankaVl()),Double.toString(itiranBean.getMinBaitankaVl()))%>" tabindex="-1" readonly>
				</td>

				<!------ 産地 ------>
				<td nowrap align="center" width="45" >
					<input type="hidden" name="santi_cd<%=i%>" value="<%=HTMLUtil.toText(itiranBean.getSantiCd())%>"/>
					<input type="text" value="<%=itiranBean.getSantiNa()%>" size="5" style="border-width: 0px; width: 35px; " tabindex="-1" readonly />
				</td>

				<!------ 等級 ------>
				<td nowrap align="center" width="40" >
					<input type="hidden" name="tokaikyu_cd<%=i%>" value="<%=HTMLUtil.toText(itiranBean.getTokaikyuCd())%>"/>
					<input type="text" value="<%=itiranBean.getTokaikyuNa()%>" size="5" style="border-width: 0px; width: 35px;" tabindex="-1" readonly />
				</td>

				<!------ 規格 ------>
				<td nowrap align="center" width="40" >
					<input type="hidden" name="kikaku_cd<%=i%>" value="<%=HTMLUtil.toText(itiranBean.getKikakuCd())%>"/>
					<input type="text" value="<%=itiranBean.getKikakuNa()%>" size="5" style="border-width: 0px; width: 35px;" tabindex="-1" readonly />
				</td>

				<!------ 納品区分 ------>
				<td nowrap  align="center" width="55" >
					<input type="text" value="<%=itiranBean.getButuryuNm()%>" size="7" style="border-width: 0px; width: 45px;" tabindex="-1" readonly />
				</td>

				<!------ 便 ------>
				<td nowrap align="center" width="20">
					<%=itiranBean.getBinKb()%>
				</td>

				<!------ 納品日 ------>
				<td nowrap align="center" width="80" >
					<%= HTMLUtil.toDate(itiranBean.getNohinDt(), "yyyy/MM/dd")%>
				</td>

				<!------ 削除 ------>
				<td nowrap width="40" align="center">
						<%	if(RoleUtil.isTorihikisakiFurumai(role)) {	%>
						    <%	if(itiranBean.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
						    		|| itiranBean.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
						    		|| itiranBean.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
						    		|| itiranBean.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
						    	<%	if( itiranBean.getNohinSyoriKb().getCode().equals(NohinSyoriKb.JYUCHU_MI.getCode()) ) {	%>
						            	<img src="./images/b_delete_w.png" width="40" height="20" align="absmiddle" alt="削除" onClick="javascript:doDeleteTran('<%=i%>')"/>
							    <%	}	%>
							<%	}	%>
						<%	} else {	%>
								<%	if( itiranBean.getNohinSyoriKb().getCode().equals(NohinSyoriKb.JYUCHU_MI.getCode()) ) {	%>
						            	<img src="./images/b_delete_w.png" width="40" height="20" align="absmiddle" alt="削除" onClick="javascript:doDeleteTran('<%=i%>')"/>
							    <%	}	%>
						<%	}	%>
				</td>
				<td nowrap width="40" align="center">
						<%	if(RoleUtil.isTorihikisakiFurumai(role)) {	%>
						    <%	if(itiranBean.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
						    		|| itiranBean.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
						    		|| itiranBean.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
						    		|| itiranBean.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
						        	<img src="./images/b_copy_w.gif" width="40" height="20" align="absmiddle" alt="コピー" onClick="javascript:doCopyTran('<%=i%>')"/>
							<%	}	%>
						<%	} else {	%>
								<img src="./images/b_copy_w.gif" width="40" height="20" align="absmiddle" alt="コピー" onClick="javascript:doCopyTran('<%=i%>')"/>
						<%	}	%>
				</td>

				<!------ 登録者 ------>
				<td nowrap width="225" align="center" >
					<INPUT type="text" readOnly size=40 style="border-width: 0px; width: 210px;" value="<%= HTMLUtil.toLabel(itiranBean.getInsertUserId().trim() + ' ' + itiranBean.getInsertUserNa()) %>" tabIndex ="-1">
				</td>
				<!------ 確定者 ------>
				<td nowrap width="225" align="center" >
					<INPUT type="text" readOnly size=40 style="border-width: 0px; width: 210px;" value="<%= HTMLUtil.toLabel(itiranBean.getJuchuKakuteiUserId().trim() + ' ' + itiranBean.getJuchuKakuteiUserNa()) %>" tabIndex ="-1">
				</td>
			</tr>

	<%}%>

		</table>
		</div>
	</div>
	</br>

\	<DIV align="center">
		<font color="#0000FF">
			<B>納品</B>：未確定＝緊急発注データ 未確定&emsp;確定＝緊急発注データ　確定済&emsp;出力済＝ 納品リスト出力済
		</font>
	</DIV>

</td>
</tr>
</table>

	<table border="0" cellspacing="0" cellpadding="0" style="line-height: 14px;">
		<td align="center">
			<div class="navi_data">
				<%
				String first = "";
				String prev = "";
				String next = "";
				String last = "";

				if( emergencySyohinItiranBh.getCurrentPageNumber() == 1 ) {
					first = "disabled";
					prev = "disabled";
				}
				if( emergencySyohinItiranBh.getCurrentPageNumber() == emergencySyohinItiranBh.getLastPageNumber() || emergencySyohinItiranBh.getLastPageNumber() == 0) {
					next = "disabled";
					last = "disabled";
				}
				%>
				<input type="button" style="width: 38px; height: 21px;" value="先頭" <%= first %> onClick="changePage('first');" />
				<input type="button" style="width: 76px; height: 21px;" value="前の<%= EmergencyItiranStatus.LOWS_IN_PAGE %>件" <%= prev %> onClick="changePage('prev');" />
				<input type="button" style="width: 76px; height: 21px;" value="次の<%= EmergencyItiranStatus.LOWS_IN_PAGE %>件" <%= next %> onClick="changePage('next');" />
				<input type="button" style="width: 38px; height: 21px;" value="最終" <%= last %> onClick="changePage('last');" />
			</div>
			<span class="size12">(<%= emergencySyohinItiranBh.getMaxRows() %>件中<%= emergencySyohinItiranBh.getStartRowInPage() %>～<%= emergencySyohinItiranBh.getEndRowInPage() %>件目）</span>
		</td>
	</table>

	<br>

	<input type="button" name="" style="width: 97px;" value="&emsp;確&emsp;定&emsp;" class="btn" onClick="decideCommand();"/>
	<input type="button" name="" style="width: 118px;" value="納品リスト出力" class="btn" onClick="deliveryListCommand(this.form);"/>

<%
}
%>
</form>
<!-- Body END -->
<!---- システム共通フッター START ---->
	<table border="2" cellspacing="0" cellpadding="0" class="kensaku tableNote" width="500">
		<tr>
			<td>
			<br>
            &emsp;<font color="#0000FF"><b>※検索について</b><br/>
			&emsp;　納品日（自）のみ指定されている場合は、指定された納品日のみが対象となります。<br />
			&emsp;&emsp;</font>
            <br>
			<br />
			</td>
			<td style="width: 3px; border-width: 0px;"></td>
		</tr>
		<tr style="height: 1px;"></tr>
	</table>
<!---- システム共通フッター END ---->
</table>
</body>