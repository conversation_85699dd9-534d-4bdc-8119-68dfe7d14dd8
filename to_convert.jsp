<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onload="init();putOnLoadDisplay();">
	<form name="MainForm" method="post" action="app">
		<table cellspacing="0" cellpadding="0" border="0">
			<tr>
				<td>
					<jsp:include page="ptl000001_Header.jsp?PARAM=緊急発注登録（店別）（SSN04006）"></jsp:include>
				</td>
			</tr>
			<tr>
				<td align="center">
					<jsp:include page="rbs00000_common.jsp" />
					<!-- 原価チェック用 ---->
					<input type="hidden" name="GEN01_FUTOGO" value="<%=wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN01")).getFutogo() : ""%>">
					<input type="hidden" name="GEN01_VALUE" value="<%=wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN01")).getValueString() : ""%>">
					<input type="hidden" name="GEN01_MSG" value="<%=wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN01")).getClMsg() : ""%>">
					<input type="hidden" name="GEN02_FUTOGO" value="<%=wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN02")).getFutogo() : ""%>">
					<input type="hidden" name="GEN02_VALUE" value="<%=wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN02")).getValueString() : ""%>">
					<input type="hidden" name="GEN02_MSG" value="<%=wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN02")).getClMsg() : ""%>">
					<input type="hidden" name="GEN03_FUTOGO" value="<%=wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN03")).getFutogo() : ""%>">
					<input type="hidden" name="GEN03_VALUE" value="<%=wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN03")).getValueString() : ""%>">
					<input type="hidden" name="GEN03_MSG" value="<%=wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN03")).getClMsg() : ""%>">
					<input type="hidden" name="GEN04_FUTOGO" value="<%=wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN04")).getFutogo() : ""%>">
					<input type="hidden" name="GEN04_VALUE" value="<%=wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN04")).getValueString() : ""%>">
					<input type="hidden" name="GEN04_MSG" value="<%=wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean) wkGenkaCheck.get("GEN04")).getClMsg() : ""%>">
					<%
						if (emergencyTorokuStatus.getDisplayMode().equals("Insert")) {
						//登録ヘッダ
					%>
					<div class="term">
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>伝票番号</th>
								<td align="left" class="string_label" colspan="3">※自動付与されます</td>
								<th>*取引先</th>
								<td colspan="3">
									<%
										//取引先権限振分
									if (RoleUtil.isTorihikisakiFurumai(role)) {
									%>
									<input type="text" name="term_torihikisaki_cd" value="<%if (emergencyTorokuStatus.getTorihikisakiCd() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiCd()));%>" tabindex="-1" readOnly>
									<input type="text" name="term_torihikisaki_na" value="<%if (emergencyTorokuStatus.getTorihikisakiNa() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiNa()));%>" tabindex="-1" readOnly>
									<%
										} else {
									%>
									<input type="text" name="term_torihikisaki_cd" value="<%if (emergencyTorokuStatus.getTorihikisakiCd() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiCd()));%>" maxlength="<%=torihikisakiCdLen%>"
										<%=emergencyTorokuStatus.isTorihikisakiReadOnly() ? " id=\"no_input_text\" readOnly" : ""%>>
									<input type="text" name="term_torihikisaki_na" value="<%if (emergencyTorokuStatus.getTorihikisakiNa() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiNa()));%>" tabindex="-1" readOnly>
									<%
										if (!emergencyTorokuStatus.isTorihikisakiReadOnly()) {
									%>
									<img src="./images/b_select_w.png" align="absmiddle" alt="選択" onClick="pop_siireSel('MainForm.term_torihikisaki_cd', 'MainForm.term_torihikisaki_na');" />
									<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="clear2(MainForm.term_torihikisaki_cd, MainForm.term_torihikisaki_na);" />
									<%
										}
									%>
									<%
										}
									%>
								</td>
							</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>*部門</th>
								<td colspan="3">
									<input type="text" name="term_bunrui1_cd" value="<%if (emergencyTorokuStatus.getBunrui1Cd() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getBunrui1Cd()));%>" maxlength="<%=bunrui1CdLen%>" onChange="changeModifiedCondition()" />
									<input type="text" name="term_bunrui1_na" value="<%if (emergencyTorokuStatus.getBunrui1Na() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getBunrui1Na()));%>" tabindex="-1" readonly />
									<img src="./images/b_select_w.png" align="absmiddle" alt="選択" onClick="pop_DptLineClass('MainForm.term_bunrui1_cd', 'MainForm.term_bunrui1_na','','','','',1,MainForm.term_bunrui1_cd.value,MainForm.term_bunrui1_na.value);" />
									<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('term_bunrui1')" />
								</td>
								<th>*相場区分</th>
								<td colspan="3">
									<%
										String soba_selected = "";
									String hisoba_selected = "";
									if (emergencyTorokuStatus.getSobaKb() != null) {
										if (emergencyTorokuStatus.getSobaKb().equals(SobaKb.SOBA)) {
											soba_selected = "selected";
										} else if (emergencyTorokuStatus.getSobaKb().equals(SobaKb.HISOBA)) {
											hisoba_selected = "selected";
										}
									}
									%>
									<select name="term_soba_kb" onChange="changeModifiedCondition()">
										<option value="<%=HTMLUtil.toLabel(SobaKb.SOBA.getCode())%>" <%=soba_selected%>><%=HTMLUtil.toLabel(SobaKb.SOBA.toString())%></option>
									</select>
								</td>
							</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>*店舗</th>
								<td colspan="3">
									<input type="text" name="term_tenpo_cd" value="<%if (emergencyTorokuStatus.getTenpoCd() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getTenpoCd()));%>" maxlength="<%=tenpoCdLen%>" onChange="changeModifiedCondition()" />
									<input type="text" name="term_tenpo_na" value="<%if (emergencyTorokuStatus.getTenpoNa() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getTenpoNa()));%>" tabindex="-1" readonly />
									<img src="./images/b_select_w.png" align="absmiddle" alt="選択" onClick="pop_tenpoSel('MainForm.term_tenpo_cd', 'MainForm.term_tenpo_na', '');" />
									<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('term_tenpo')" />
								</td>
								<th>*納品区分</th>
								<td align="left">
									<select name="term_buturyu_kb" onChange="changeModifiedCondition(); changeIkkatuFg();">
										<option value=""></option>
										<%
											if (maButuryuBh.getMaxRows() > 0) {
											for (Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext();) {
												MaButuryuBean bean = (MaButuryuBean) ite.next();
												if (!HTMLUtil.toText(bean.getShiwakeKb().trim()).equals("3")) {
										%>
										<%
											if (RoleUtil.isTorihikisakiFurumai(role)) {
										%>
										<%
											if (!bean.getButuryuKb().equals(ButuryuKb.TC_ICHIBA.getCode())
												&& !bean.getButuryuKb().equals(ButuryuKb.TYOKUNO_ICHIBA.getCode())) {
										%>
										<option value="<%=HTMLUtil.toText(bean.getButuryuKb())%>" <%=emergencyTorokuStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ? " selected" : ""%>>
											<%=HTMLUtil.toLabel(bean.getButuryuNm())%></option>
										<%
											}
										%>
										<%
											} else {
										%>
										<option value="<%=HTMLUtil.toText(bean.getButuryuKb())%>" <%=emergencyTorokuStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ? " selected" : ""%>>
											<%=HTMLUtil.toLabel(bean.getButuryuNm())%></option>
										<%
											}
										%>
										<%
											}
										%>
										<%
											}
										%>
										<%
											}
										%>
									</select>
								</td>
								<th>経由センター</th>
								<td align="left">
									<select name="term_center_cd" onChange="changeModifiedCondition();">
										<option value=""></option>
										<%
											if (maCenterBh.getMaxRows() > 0) {
											for (Iterator ite = maCenterBh.getBeanIterator(); ite.hasNext();) {
												MaCenterBean bean = (MaCenterBean) ite.next();
										%>
										<option value="<%=HTMLUtil.toText(bean.getCenterCd())%>" <%=emergencyTorokuStatus.getCenterCd().trim().equals(bean.getCenterCd().trim()) ? " selected" : ""%>>
											<%=HTMLUtil.toLabel(bean.getCenterShortNa())%></option>
										<%
											}
										%>
										<%
											}
										%>
									</select>
								</td>
							</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>*発注日</th>
								<td>
									<input type="text" name="term_hachu_dt" value="<%if (emergencyTorokuStatus.getHachuDt() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getHachuDt()));%>" maxlength="8" onChange="changeModifiedCondition()" />
									<img src="./images/calendar.gif" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.term_hachu_dt);" />
									<small>（YYYYMMDD）</small>
								</td>
								<th>*納品日</th>
								<td>
									<input type="text" name="term_nohin_dt" value="<%if (emergencyTorokuStatus.getNohinDt() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getNohinDt()));%>" maxlength="8" onChange="changeModifiedCondition()" />
									<img src="./images/calendar.gif" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.term_nohin_dt);" />
									<small>（YYYYMMDD）</small>
								</td>
								<th>*便</th>
								<td align="left">
									<input type="text" name="term_bin_nm" maxlength="1" class="numeric" value="<%=HTMLUtil.toText(emergencyTorokuStatus.getBinNm())%>" onChange="changeModifiedCondition()" />
								</td>
							</tr>
						</table>
					</div>
					<%
						} else {
					//参照ヘッダ
					%>
					<div class="term">
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>伝票番号</th>
								<td align="left" class="string_label" colspan="3"><%=HTMLUtil.toText(dtHachuBean.getDenpyoNb())%></td>
								<th>取引先</th>
								<td colspan="3">
									<input type="text" name="term_torihikisaki_cd" value="<%if (emergencyTorokuStatus.getTorihikisakiCd() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiCd()));%>" tabindex="-1" readOnly />
									<input type="text" name="term_torihikisaki_na" value="<%if (emergencyTorokuStatus.getTorihikisakiNa() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiNa()));%>" tabindex="-1" readOnly>
								</td>
							</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>部門</th>
								<td colspan="3">
									<input type="text" name="bunrui1_cd" value="<%if (emergencyTorokuStatus.getBunrui1Cd() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getBunrui1Cd()));%>" maxlength="<%=bunrui1CdLen%>" id="no_input_text" tabindex="-1" readOnly />
									<input type="text" name="bunrui1_na" value="<%if (emergencyTorokuStatus.getBunrui1Na() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getBunrui1Na()));%>" tabindex="-1" readonly />
								</td>
								<th>相場区分</th>
								<td colspan="3">
									<%
										String soba_selected = "";
									String hisoba_selected = "";
									if (emergencyTorokuStatus.getSobaKb() != null) {
										if (emergencyTorokuStatus.getSobaKb().equals(SobaKb.SOBA)) {
											soba_selected = "selected";
										} else if (emergencyTorokuStatus.getSobaKb().equals(SobaKb.HISOBA)) {
											hisoba_selected = "selected";
										}
									}
									%>
									<select name="soba_kb" disabled>
										<option value="<%=HTMLUtil.toLabel(SobaKb.SOBA.getCode())%>" <%=soba_selected%>><%=HTMLUtil.toLabel(SobaKb.SOBA.toString())%></option>
										<option value="<%=HTMLUtil.toLabel(SobaKb.HISOBA.getCode())%>" <%=hisoba_selected%>><%=HTMLUtil.toLabel(SobaKb.HISOBA.toString())%></option>
									</select>
								</td>
							</tr>
						</table>
						<table border="0" cellspacing="1" cellpadding="0" class="term">
							<tr>
								<th>店舗</th>
								<td colspan="3">
									<input type="text" name="tenpo_cd" value="<%if (emergencyTorokuStatus.getTenpoCd() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getTenpoCd()));%>" maxlength="<%=tenpoCdLen%>" tabindex="-1" readOnly />
									<input type="text" name="tenpo_na" value="<%if (emergencyTorokuStatus.getTenpoNa() != null)
	out.print(HTMLUtil.toText(emergencyTorokuStatus.getTenpoNa()));%>" tabindex="-1" readonly />
								</td>
								<th>納品区分</th>
								<td align="left">
									<select name="buturyu_kb" disabled>
										<option value=""></option>
										<%
											if (maButuryuBh.getMaxRows() > 0) {
											for (Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext();) {
												MaButuryuBean bean = (MaButuryuBean) ite.next();
												if (!HTMLUtil.toText(bean.getShiwakeKb().trim()).equals("3")) {
										%>
										<option value="<%=HTMLUtil.toText(bean.getButuryuKb())%>" <%=emergencyTorokuStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ? " selected" : ""%>>
											<%=HTMLUtil.toLabel(bean.getButuryuNm())%></option>
										<%
											}
										%>
										<%
											}
										%>
										<%
											}
										%>
									</select>
								</td>
								<th>経由センター</th>
								<td align="left">
									<select name="center_cd" disabled>
										<option value=""></option>
										<%
											if (maCenterBh.getMaxRows() > 0) {
											for (Iterator ite = maCenterBh.getBeanIterator(); ite.hasNext();) {
												MaCenterBean bean = (MaCenterBean) ite.next();
										%>
										<option value="<%=HTMLUtil.toText(bean.getCenterCd())%>" <%=emergencyTorokuStatus.getCenterCd().trim().equals(bean.getCenterCd().trim()) ? " selected" : ""%>>
											<%=HTMLUtil.toLabel(bean.getCenterShortNa())%></option>
										<%
											}
										%>
										<%
											}
										%>
									</select>
								</td>
							</tr>
						</table>
			<table border="0" cellspacing="0" cellpadding="0" class="kensaku">
				<tr>
					<th>発注日</th>
					<td>
						<input type="hidden" name="hachu_dt" value="<%=emergencyTorokuStatus.getHachuDt()%>">
						<input type="text" name="hachu_dt_display" value="<%if (emergencyTorokuStatus.getHachuDt() != null)
	out.print(HTMLUtil.toDate(emergencyTorokuStatus.getHachuDt(), "yyyy/MM/dd"));%>" tabindex="-1" readOnly />
						<small>（YYYYMMDD）</small>
					</td>
					<th>納品日</th>
					<td>
						<input type="hidden" name="nohin_dt" value="<%=emergencyTorokuStatus.getNohinDt()%>">
						<input type="text" name="nohin_dt_display" value="<%if (emergencyTorokuStatus.getNohinDt() != null)
	out.print(HTMLUtil.toDate(emergencyTorokuStatus.getNohinDt(), "yyyy/MM/dd"));%>" tabindex="-1" readOnly />
						<small>（YYYYMMDD）</small>
					</td>
					<th>便</th>
					<td align="left">
						<input type="text" name="bin_nm" maxlength="1" class="numeric" value="<%=HTMLUtil.toText(emergencyTorokuStatus.getBinNm())%>" tabindex="-1" readOnly />
					</td>
				</tr>
			</table>
			<%
				}
			%>
			<div id="messageBase" class="term">
				<table class="term_msg_area" cellpadding="0" cellspacing="0" border="0" align="center">
					<tr>
						<td align="center">
							<jsp:include page="InfoStringMdWare.jsp" />
						</td>
					</tr>
				</table>
			</div>
			<%
				if (!emergencyTorokuStatus.getDisplayMode().equals("Reference")) {
				//登録明細
			%>
			<div class="term">
				<table class="term_btn_area">
					<tbody>
						<tr>
							<td>
								<center>
									<input type="button" value="明細情報取得" name="" class="controlButton" onClick="doSearchTran();">
								</center>
							</td>
						</tr>
					</tbody>
				</table>
			</div>
			<div class="list1" align="left">
				<div id="result_scroll_area">
					<table class="list1_search" cellpadding="0" cellspacing="0">
						<tr>
							<th rowspan="2">No</th>
							<th align="center">商品コード</th>
							<th align="center">商品名</th>
							<th align="center">入数</th>
							<th align="center" rowspan="2">発注単位</th>
							<th align="center" rowspan="2">納品数</th>
							<th align="center" rowspan="2">納品数量</th>
							<th align="center" rowspan="2">原単価</th>
							<%
								// 取引先は非表示
							if (RoleUtil.isTorihikisakiFurumai(role)) {
							%>
							<th align="center" rowspan="2"></th>
							<%
								} else {
							%>
							<th align="center" rowspan="2">出庫単価</th>
							<%
								}
							%>
							<th align="center" rowspan="2">売単価</th>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role)) {
							%>
							<%
								if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
									|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
									|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
									|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {
							%>
							<th align="center" rowspan="2">クリア</th>
							<%
								}
							%>
							<%
								} else {
							%>
							<th align="center" rowspan="2">クリア</th>
							<%
								}
							%>
						</tr>
						<tr>
							<th colspan="3">
								<table border="0" cellspacing="1" cellpadding="0" class="data">
									<tr>
										<th align="center">産地</th>
										<th align="center">等級</th>
										<th align="center">規格</th>
									</tr>
								</table>
							</th>
						</tr>
						<%
							DtHachuMeisaiBean dtHachuMeisaiBean = null;

						BigDecimal hachuKeiQt = new BigDecimal("0");
						BigDecimal hachuSuryoKeiQt = new BigDecimal("0");
						BigDecimal genkaKeiQt = new BigDecimal("0");
						BigDecimal idoGenkaKeiQt = new BigDecimal("0");
						BigDecimal baikaKeiQt = new BigDecimal("0");

						for (int i = 0; i < dtHachuMeisaiList.size(); i++) {
							dtHachuMeisaiBean = (DtHachuMeisaiBean) dtHachuMeisaiList.get(i);
							hachuKeiQt = hachuKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getHachuQtString()));
							hachuSuryoKeiQt = hachuSuryoKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getHachuSuryoQtString()));
							genkaKeiQt = genkaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getGenkaVlString()));
							idoGenkaKeiQt = idoGenkaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getIdoGenkaVlString()));
							baikaKeiQt = baikaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getBaikaVlString()));
						%>
						<tr>
							<td align="center" rowspan="2" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<%=i + 1%>
							</td>
							<%-- 商品コード --%>
							<td align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="hidden" name="syohin_cd_<%=i%>" value="<%if (dtHachuMeisaiBean.getSyohinCd() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSyohinCd()));%>">
								<input type="text" name="hachu_syohin_cd_<%=i%>" maxlength="13" value="<%if (dtHachuMeisaiBean.getHachuSyohinCd() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getHachuSyohinCd()));%>" onChange="changeModifiedCondition()">
							</td>
							<%-- 商品名称 --%>
							<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="syohin_na_<%=i%>" maxlength="40" value="<%if (dtHachuMeisaiBean.getSyohinNa() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSyohinNa()));%>" />
								<input type="text" name="hi_kikaku_na_<%=i%>" value="<%if (dtHachuMeisaiBean.getHiKikakuNa() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getHiKikakuNa()));%>" tabindex="-1" readonly />
								<img src="./images/b_select_w.png" align="absmiddle" alt="選択"
									onClick="if(checkBunrui1())if(checkSobaKb())
						pop_syohinSel(
						'MainForm.hachu_syohin_cd_<%=i%>',
						'MainForm.syohin_na_<%=i%>',
						'MainForm.hi_kikaku_na_<%=i%>',
						MainForm.bunrui1_cd,
						'','',
						document.MainForm.soba_kb.value,
						'',
					    document.MainForm.nohin_dt
					);" />
							</td>
							<%-- 入数 --%>
							<td align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="irisu_qt_<%=i%>" maxlength="4" value="<%=HTMLUtil.toLabel(dtHachuMeisaiBean.getIrisuQtString(), "###0")%>" class="numeric" />
							</td>
							<%-- 発注単位 --%>
							<td rowspan="2" align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<select name="hachu_tani_cd_<%=i%>" onChange="changeModifiedCondition()">
									<option value=""></option>
									<%
										if (nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
										List selectList = nohinTaniList.getBeanList();
										for (int k = 0; k < selectList.size(); k++) {
											MaHachuTaniBean bean = (MaHachuTaniBean) selectList.get(k);
											String hachu_tani_cd = bean.getHachuTaniCd();
											String selected = "";

											if (hachu_tani_cd != null && dtHachuMeisaiBean.getHachuTaniCd().equals(hachu_tani_cd)) {
										selected = "selected";
											}
									%>
									<option value="<%=hachu_tani_cd%>" <%=selected%>><%=bean.getHachuTaniNa()%>
									</option>
									<%
										}
									}
									%>
								</select>
							</td>
							<%-- 納品数 --%>
							<td align="center" rowspan="2" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="hachu_qt_<%=i%>" maxlength="4" value="<%if (!(dtHachuMeisaiBean.getHachuQt() == 0 && dtHachuMeisaiBean.getHachuSuryoQt() == 0))
	out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getHachuQtString(), "##0"));%>"
									class="numeric" onChange="changeModifiedCondition();">
							</td>
							<%-- 納品数量 --%>
							<td align="center" rowspan="2" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="hachu_suryo_qt_<%=i%>" maxlength="9" value="<%if (dtHachuMeisaiBean.getHachuSuryoQt() != 0)
	out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getHachuSuryoQtString(), "####0.##"));%>" class="numeric"
									style="ime-mode: disabled; width: 67px;" onChange="changeModifiedCondition();">
							</td>
							<%-- 原単価 --%>
							<td align="center" rowspan="2" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="gentanka_vl_<%=i%>" maxlength="10" value="<%if (dtHachuMeisaiBean.getKakuGentankaVl() != 0)
	out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getKakuGentankaVlString(), "######0.00"));%>" class="numeric"
									style="ime-mode: disabled; width: 67px;" onChange="changeModifiedCondition();" />
							</td>
							<%-- 出庫単価 --%>
							<td align="center" rowspan="2" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<%
									// 取引先は非表示
								if (RoleUtil.isTorihikisakiFurumai(role)) {
								%>
								<input type="hidden" name="shukatanka_vl_<%=i%>" value="<%if (dtHachuMeisaiBean.getIdoGentankaVl() != 0)
	out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getIdoGentankaVlString(), "######0.00"));%>" />
								<%
									} else {
								%>
								<input type="text" name="shukatanka_vl_<%=i%>" maxlength="10" value="<%if (dtHachuMeisaiBean.getIdoGentankaVl() != 0)
	out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getIdoGentankaVlString(), "######0.00"));%>" class="numeric"
									style="ime-mode: disabled; width: 67px;" onChange="changeModifiedCondition()" />
								<%
									}
								%>
							</td>
							<%-- 売単価 --%>
							<td align="center" rowspan="2" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="baitanka_vl_<%=i%>" maxlength="7" value="<%if (dtHachuMeisaiBean.getKakuBaitankaVl() != 0)
	out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getKakuBaitankaVlString(), "#######0"));%>" class="numeric"
									style="ime-mode: disabled; width: 57px;" onChange="changeModifiedCondition();" />
							</td>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role)) {
							%>
							<%
								if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
									|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
									|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
									|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {
							%>
							<td align="center" rowspan="2" rowspan="2" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="meisaiClear(<%=i%>);" />
							</td>
							<%
								}
							%>
							<%
								} else {
							%>
							<td align="center" rowspan="2" rowspan="2" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="meisaiClear(<%=i%>);" />
							</td>
							<%
								}
							%>
						</tr>
						<tr>
							<td colspan="3" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<table border="0" cellspacing="1" cellpadding="0">
									<tr>
										<%-- 産地 --%>
										<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<input type="hidden" name="santi_cd_<%=i%>" value="<%if (dtHachuMeisaiBean.getSantiCd() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSantiCd()));%>" />
											<input type="text" name="santi_na_<%=i%>" value="<%if (dtHachuMeisaiBean.getSantiNa() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSantiNa()));%>" tabindex="-1" readonly onChange="changeModifiedCondition()" />
											<img src="./images/b_select_w.png" align="absmiddle" alt="選択" onClick="if(checkTorihikisaki()) pop_santiSel('MainForm.santi_cd_<%=i%>','MainForm.santi_na_<%=i%>',MainForm.torihikisaki_cd.value);" />
										</td>
										<%-- 等級 --%>
										<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<input type="hidden" name="tokaikyu_cd_<%=i%>" value="<%if (dtHachuMeisaiBean.getTokaikyuCd() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getTokaikyuCd()));%>" />
											<input type="text" name="tokaikyu_na_<%=i%>" value="<%if (dtHachuMeisaiBean.getTokaikyuNa() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getTokaikyuNa()));%>" tabindex="-1" readonly onChange="changeModifiedCondition()" />
											<img src="./images/b_select_w.png" align="absmiddle" alt="選択" onClick="if(checkTorihikisaki()) pop_tokaiqSel('MainForm.tokaikyu_cd_<%=i%>','MainForm.tokaikyu_na_<%=i%>',MainForm.torihikisaki_cd.value);" />
										</td>
										<%-- 規格 --%>
										<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<input type="hidden" name="kikaku_cd_<%=i%>" value="<%if (dtHachuMeisaiBean.getKikakuCd() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getKikakuCd()));%>" />
											<input type="text" name="kikaku_na_<%=i%>" value="<%if (dtHachuMeisaiBean.getKikakuNa() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getKikakuNa()));%>" tabindex="-1" readonly onChange="changeModifiedCondition()" />
											<img src="./images/b_select_w.png" align="absmiddle" alt="選択" onClick="if(checkTorihikisaki()) pop_kikakuSel('MainForm.kikaku_cd_<%=i%>','MainForm.kikaku_na_<%=i%>',MainForm.torihikisaki_cd.value);" />
										</td>
										<%
											if (RoleUtil.isTorihikisakiFurumai(role)) {
										%>
										<%
											if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
												|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
												|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
												|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {
										%>
										<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="STKClear(<%=i%>);" />
										</td>
										<%
											}
										%>
										<%
											} else {
										%>
										<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="STKClear(<%=i%>);" />
										</td>
										<%
											}
										%>
									</tr>
								</table>
							</td>
						</tr>
						<%
							}

						for (int j = dtHachuMeisaiList.size(); j < 9; j++) {
						%>
						<tr>
							<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<%=j + 1%>
							</td>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
									|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
							%>
							<td align="center" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="hidden" name="syohin_cd_<%=j%>" value="">
								<input type="text" name="hachu_syohin_cd_<%=j%>" maxlength="13" value="" tabindex="-1" readonly>
							</td>
							<%
								} else {
							%>
							<td align="center" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="hidden" name="syohin_cd_<%=j%>" value="">
								<input type="text" name="hachu_syohin_cd_<%=j%>" maxlength="13" value="" onChange="changeModifiedCondition()">
							</td>
							<%
								}
							%>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
									|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
							%>
							<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="syohin_na_<%=j%>" value="" tabindex="-1" readonly />
								<input type="text" name="hi_kikaku_na_<%=j%>" value="" tabindex="-1" readonly />
							</td>
							<%
								} else {
							%>
							<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="syohin_na_<%=j%>" maxlength="40" value="" />
								<input type="text" name="hi_kikaku_na_<%=j%>" value="" tabindex="-1" readonly />
								<img src="./images/b_select_w.png" align="absmiddle" alt="選択"
									onClick="if(checkBunrui1())if(checkSobaKb())
								pop_syohinSel(
								'MainForm.hachu_syohin_cd_<%=j%>',
								'MainForm.syohin_na_<%=j%>',
								'MainForm.hi_kikaku_na_<%=j%>',
								MainForm.bunrui1_cd,
								'','',
								document.MainForm.soba_kb.value,
								'',
							    document.MainForm.nohin_dt
							);" />
							</td>
							<%
								}
							%>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
									|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
							%>
							<td align="center" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="irisu_qt_<%=j%>" maxlength="4" value="" class="numeric" tabIndex="-1" readonly>
							</td>
							<%
								} else {
							%>
							<td align="center" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="irisu_qt_<%=j%>" maxlength="4" value="" class="numeric" />
							</td>
							<%
								}
							%>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
									|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
							%>
							<td rowspan="2" align="center" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<select name="hachu_tani_cd_<%=j%>" disabled>
									<option value=""></option>
									<%
										if (nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
										List selectList = nohinTaniList.getBeanList();
										for (int l = 0; l < selectList.size(); l++) {
											MaHachuTaniBean bean = (MaHachuTaniBean) selectList.get(l);
											String hachu_tani_cd = bean.getHachuTaniCd();
											String selected = "";
											if (searchData.getParameter("hachu_tani_cd") != null && hachu_tani_cd != null
											&& dtHachuMeisaiBean.getHachuTaniCd().equals(hachu_tani_cd)) {
										selected = "selected";
											}
									%>
									<option value="<%=hachu_tani_cd%>" <%=selected%>><%=bean.getHachuTaniNa()%>
									</option>
									<%
										}
									}
									%>
								</select>
							</td>
							<%
								} else {
							%>
							<td rowspan="2" align="center" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<select name="hachu_tani_cd_<%=j%>" onChange="changeModifiedCondition()">
									<option value=""></option>
									<%
										if (nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
										List selectList = nohinTaniList.getBeanList();
										for (int l = 0; l < selectList.size(); l++) {
											MaHachuTaniBean bean = (MaHachuTaniBean) selectList.get(l);
											String hachu_tani_cd = bean.getHachuTaniCd();
											String selected = "";

											if (searchData.getParameter("hachu_tani_cd") != null && hachu_tani_cd != null
											&& dtHachuMeisaiBean.getHachuTaniCd().equals(hachu_tani_cd)) {
										selected = "selected";
											}
									%>
									<option value="<%=hachu_tani_cd%>" <%=selected%>><%=bean.getHachuTaniNa()%>
									</option>
									<%
										}
									}
									%>
								</select>
							</td>
							<%
								}
							%>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
									|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
							%>
							<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="hachu_qt_<%=j%>" maxlength="4" value="" class="numeric" tabindex="-1" readonly />
							</td>
							<%
								} else {
							%>
							<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="hachu_qt_<%=j%>" maxlength="4" value="" class="numeric" onChange="changeModifiedCondition();">
							</td>
							<%
								}
							%>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
									|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
							%>
							<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="hachu_suryo_qt_<%=j%>" maxlength="9" value="" class="numeric" tabindex="-1" readonly />
							</td>
							<%
								} else {
							%>
							<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="hachu_suryo_qt_<%=j%>" maxlength="9" value="" class="numeric" onChange="changeModifiedCondition();">
							</td>
							<%
								}
							%>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
									|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
							%>
							<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="gentanka_vl_<%=j%>" maxlength="10" value="" class="numeric" tabindex="-1" readonly />
							</td>
							<%
								} else {
							%>
							<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="gentanka_vl_<%=j%>" maxlength="10" value="" class="numeric" onChange="changeModifiedCondition();" />
							</td>
							<%
								}
							%>
							<%-- 出庫単価 --%>
							<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<%
									// 取引先は非表示
								if (RoleUtil.isTorihikisakiFurumai(role)) {
								%>
								<input type="hidden" name="shukatanka_vl_<%=j%>" value="" tabindex="-1" disabled readonly>
								<%
									} else {
								%>
								<input type="text" name="shukatanka_vl_<%=j%>" maxlength="10" value="" class="numeric" onChange="changeModifiedCondition()" />
								<%
									}
								%>
							</td>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
									|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
							%>
							<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="baitanka_vl_<%=j%>" maxlength="7" value="" class="numeric" tabindex="-1" readonly />
							</td>
							<%
								} else {
							%>
							<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<input type="text" name="baitanka_vl_<%=j%>" maxlength="7" value="" class="numeric" onChange="changeModifiedCondition();" />
							</td>
							<%
								}
							%>
							<%
								if (RoleUtil.isTorihikisakiFurumai(role)) {
							%>
							<%
								if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
									|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
									|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
									|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {
							%>
							<td rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="meisaiClear(<%=j%>);" />
							</td>
							<%
								}
							%>
							<%
								} else {
							%>
							<td rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="meisaiClear(<%=j%>);" />
							</td>
							<%
								}
							%>
						</tr>
						<tr>
							<td colspan="3" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
								<table border="0" cellspacing="1" cellpadding="0">
									<tr>
										<%
											if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
												|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
										%>
										<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<input type="hidden" name="santi_cd_<%=j%>" value="" />
											<input type="text" name="santi_na_<%=j%>" value="" tabindex="-1" readonly />
										</td>
										<%
											} else {
										%>
										<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<input type="hidden" name="santi_cd_<%=j%>" value="" />
											<input type="text" name="santi_na_<%=j%>" value="" tabindex="-1" readonly onChange="changeModifiedCondition()" />
											<img src="./images/b_select_w.png" align="absmiddle" alt="選択" onClick="if(checkTorihikisaki()) pop_santiSel('MainForm.santi_cd_<%=j%>','MainForm.santi_na_<%=j%>',MainForm.torihikisaki_cd.value);" />
										</td>
										<%
											}
										%>
										<%
											if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
												|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
										%>
										<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<input type="hidden" name="tokaikyu_cd_<%=j%>" value="" />
											<input type="text" name="tokaikyu_na_<%=j%>" value="" tabindex="-1" readonly />
										</td>
										<%
											} else {
										%>
										<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<input type="hidden" name="tokaikyu_cd_<%=j%>" value="" />
											<input type="text" name="tokaikyu_na_<%=j%>" value="" tabindex="-1" readonly onChange="changeModifiedCondition()" />
											<img src="./images/b_select_w.png" align="absmiddle" alt="選択" onClick="if(checkTorihikisaki()) pop_tokaiqSel('MainForm.tokaikyu_cd_<%=j%>','MainForm.tokaikyu_na_<%=j%>',MainForm.torihikisaki_cd.value);" />
										</td>
										<%
											}
										%>
										<%
											if (RoleUtil.isTorihikisakiFurumai(role) && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
												|| ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {
										%>
										<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<input type="hidden" name="kikaku_cd_<%=j%>" value="" />
											<input type="text" name="kikaku_na_<%=j%>" value="" tabindex="-1" readonly />
										</td>
										<%
											} else {
										%>
										<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<input type="hidden" name="kikaku_cd_<%=j%>" value="" />
											<input type="text" name="kikaku_na_<%=j%>" value="" tabindex="-1" readonly onChange="changeModifiedCondition()" />
											<img src="./images/b_select_w.png" align="absmiddle" alt="選択" onClick="if(checkTorihikisaki()) pop_kikakuSel('MainForm.kikaku_cd_<%=j%>','MainForm.kikaku_na_<%=j%>',MainForm.torihikisaki_cd.value);" />
										</td>
										<%
											}
										%>
										<%
											if (RoleUtil.isTorihikisakiFurumai(role)) {
										%>
										<%
											if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
												|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
												|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
												|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {
										%>
										<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="STKClear(<%=j%>);" />
										</td>
										<%
											}
										%>
										<%
											} else {
										%>
										<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
											<img src="./images/b_clear_w.png" align="absmiddle" alt="クリア" onClick="STKClear(<%=j%>);" />
										</td>
										<%
											}
										%>
									</tr>
								</table>
							</td>
						</tr>
						<%
							}
						%>
					</table>
				</div>
				<div align="center">
					<font color="#0000FF"> ※定貫商品は、入力された納品数を元に算出した納品数量で、登録されます。 </font>
				</div>
			</td>
			</tr>
		</table>
		<br>
		<table align="center" border="0" cellspacing="1" cellpadding="0" class="data">
			<tr>
				<th align="center">納品数合計</th>
				<td>
					<input type="text" name="hachu_kei_qt" value="<%=HTMLUtil.toText(hachuKeiQt.longValue(), "#,##0")%>" class="numeric" tabindex="-1" readonly>
				</td>
				<th align="center">納品数量合計</th>
				<td>
					<input type="text" name="hachu_suryo_kei_qt" value="<%=HTMLUtil.toText(hachuSuryoKeiQt.doubleValue(), "#,##0.##")%>" class="numeric" tabindex="-1" readonly>
				</td>
				<th align="center">原価金額合計</th>
				<td>
					<input type="text" name="genka_kei_qt" value="<%=HTMLUtil.toText(genkaKeiQt.longValue(), "###,###,##0")%>" class="numeric" tabindex="-1" readonly>
				</td>
				<%
					// 取引先は非表示
				if (!RoleUtil.isTorihikisakiFurumai(role)) {
				%>
				<th align="center">出庫金額合計</th>
				<td>
					<input type="text" name="ido_genka_kei_qt" value="<%=HTMLUtil.toText(idoGenkaKeiQt.longValue(), "###,###,##0")%>" class="numeric" tabindex="-1" readonly>
				</td>
				<%
					}
				%>
				<th align="center">売価金額合計</th>
				<td>
					<input type="text" name="baika_kei_qt" value="<%=HTMLUtil.toText(baikaKeiQt.longValue(), "###,###,##0")%>" class="numeric" tabindex="-1" readonly>
				</td>
			</tr>
		</table>
		<%
			} else {
		//参照明細
		%>
		<br />
		<table>
			<tr align="left">
				<td>
					<div align="left">
						<table border="0" cellspacing="1" cellpadding="0" class="data">
							<tr>
								<th rowspan="2">No</th>
								<th align="center">商品コード</th>
								<th align="center">商品名</th>
								<th align="center">入数</th>
								<th align="center" rowspan="2">発注単位</th>
								<th align="center" rowspan="2">納品数</th>
								<th align="center" rowspan="2">納品数量</th>
								<th align="center" rowspan="2">原単価</th>
								<%
									// 取引先は非表示
								if (RoleUtil.isTorihikisakiFurumai(role)) {
								%>
								<th align="center" rowspan="2"></th>
								<%
									} else {
								%>
								<th align="center" rowspan="2">出庫単価</th>
								<%
									}
								%>
								<th align="center" rowspan="2">売単価</th>
							</tr>
							<tr>
								<th colspan="3">
									<table border="0" cellspacing="1" cellpadding="0" class="data">
										<tr>
											<th align="center">産地</th>
											<th align="center">等級</th>
											<th align="center">規格</th>
										</tr>
									</table>
								</th>
							</tr>
							<%
								DtHachuMeisaiBean dtHachuMeisaiBean = null;

							BigDecimal hachuKeiQt = new BigDecimal("0");
							BigDecimal hachuSuryoKeiQt = new BigDecimal("0");
							BigDecimal genkaKeiQt = new BigDecimal("0");
							BigDecimal idoGenkaKeiQt = new BigDecimal("0");
							BigDecimal baikaKeiQt = new BigDecimal("0");

							//対象行分出力
							for (int i = 0; i < dtHachuMeisaiList.size(); i++) {
								dtHachuMeisaiBean = (DtHachuMeisaiBean) dtHachuMeisaiList.get(i);
								hachuKeiQt = hachuKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getHachuQtString()));
								hachuSuryoKeiQt = hachuSuryoKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getHachuSuryoQtString()));
								genkaKeiQt = genkaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getGenkaVlString()));
								idoGenkaKeiQt = idoGenkaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getIdoGenkaVlString()));
								baikaKeiQt = baikaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getBaikaVlString()));
							%>
							<tr>
								<td align="center" rowspan="2" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<%=i + 1%>
								</td>
								<%-- 商品コード --%>
								<td align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="hidden" name="syohin_cd_<%=i%>" value="<%if (dtHachuMeisaiBean.getSyohinCd() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSyohinCd()));%>">
									<input type="text" name="hachu_syohin_cd_<%=i%>" maxlength="13" value="<%if (dtHachuMeisaiBean.getHachuSyohinCd() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getHachuSyohinCd()));%>" style="ime-mode: disabled; width: 90px;"
										tabindex="-1" readonly>
								</td>
								<%-- 商品名 --%>
								<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="syohin_na_<%=i%>" value="<%if (dtHachuMeisaiBean.getSyohinNa() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSyohinNa()));%>" tabindex="-1" readonly />
									<input type="text" name="hi_kikaku_na_<%=i%>" value="<%if (dtHachuMeisaiBean.getHiKikakuNa() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getHiKikakuNa()));%>" tabindex="-1" readonly />
								</td>
								<%-- 入数 --%>
								<td align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="irisu_qt_<%=i%>" maxlength="4" value="<%=HTMLUtil.toLabel(dtHachuMeisaiBean.getIrisuQtString(), "###0")%>" class="numeric" tabIndex="-1" readonly />
								</td>
								<%-- 発注単位 --%>
								<td rowspan="2" align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<select name="hachu_tani_cd_<%=i%>" disabled>
										<option value=""></option>
										<%
											if (nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
											List selectList = nohinTaniList.getBeanList();
											for (int k = 0; k < selectList.size(); k++) {
												MaHachuTaniBean bean = (MaHachuTaniBean) selectList.get(k);
												String hachu_tani_cd = bean.getHachuTaniCd();
												String selected = "";

												if (hachu_tani_cd != null && dtHachuMeisaiBean.getHachuTaniCd().equals(hachu_tani_cd)) {
											selected = "selected";
												}
										%>
										<option value="<%=hachu_tani_cd%>" <%=selected%>><%=bean.getHachuTaniNa()%></option>
										<%
											}
										}
										%>
									</select>
								</td>
								<%-- 納品数 --%>
								<td rowspan="2" align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="hachu_qt_<%=i%>" maxlength="4" value="<%=HTMLUtil.toLabel(dtHachuMeisaiBean.getHachuQtString(), "##0")%>" class="numeric" tabindex="-1" readonly>
								</td>
								<%-- 納品数量 --%>
								<td rowspan="2" align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="hachu_suryo_qt_<%=i%>" maxlength="9" value="<%=HTMLUtil.toLabel(dtHachuMeisaiBean.getHachuSuryoQtString(), "####0.##")%>" class="numeric" tabIndex="-1" id="no_input_text" readonly>
								</td>
								<%-- 原単価 --%>
								<td rowspan="2" align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="gentanka_vl_<%=i%>" maxlength="10" value="<%=HTMLUtil.toLabel(dtHachuMeisaiBean.getKakuGentankaVlString(), "######0.00")%>" class="numeric" tabindex="-1" readonly />
								</td>
								<%-- 出庫単価 --%>
								<td rowspan="2" align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<%
										// 取引先は非表示
									if (RoleUtil.isTorihikisakiFurumai(role)) {
									%>
									<input type="hidden" name="shukatanka_vl_<%=i%>" value="<%=HTMLUtil.toLabel(dtHachuMeisaiBean.getIdoGentankaVlString(), "######0.00")%>" tabindex="-1" disabled readonly>
									<%
										} else {
									%>
									<input type="text" name="shukatanka_vl_<%=i%>" maxlength="10" value="<%=HTMLUtil.toLabel(dtHachuMeisaiBean.getIdoGentankaVlString(), "######0.00")%>" class="numeric" tabindex="-1" readOnly />
									<%
										}
									%>
								</td>
								<%-- 売単価 --%>
								<td rowspan="2" align="center" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="baitanka_vl_<%=i%>" maxlength="7" value="<%if (dtHachuMeisaiBean.getKakuBaitankaVl() != 0)
	out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getKakuBaitankaVlString(), "#######0"));%>" class="numeric" tabindex="-1"
										readonly />
								</td>
							</tr>
							<tr>
								<td colspan="3" <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<table border="0">
										<tr>
											<%-- 産地 --%>
											<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
												<input type="hidden" name="santi_cd_<%=i%>" value="<%if (dtHachuMeisaiBean.getSantiCd() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSantiCd()));%>" />
												<input type="text" name="santi_na_<%=i%>" value="<%if (dtHachuMeisaiBean.getSantiNa() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSantiNa()));%>" tabindex="-1" readonly />
											</td>
											<%-- 等級 --%>
											<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
												<input type="hidden" name="tokaikyu_cd_<%=i%>" value="<%if (dtHachuMeisaiBean.getTokaikyuCd() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getTokaikyuCd()));%>" />
												<input type="text" name="tokaikyu_na_<%=i%>" value="<%if (dtHachuMeisaiBean.getTokaikyuNa() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getTokaikyuNa()));%>" tabindex="-1" readonly />
											</td>
											<%-- 規格 --%>
											<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
												<input type="hidden" name="kikaku_cd_<%=i%>" value="<%if (dtHachuMeisaiBean.getKikakuCd() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getKikakuCd()));%>" />
												<input type="text" name="kikaku_na_<%=i%>" value="<%if (dtHachuMeisaiBean.getKikakuNa() != null)
	out.print(HTMLUtil.toText(dtHachuMeisaiBean.getKikakuNa()));%>" tabindex="-1" readonly />
											</td>
											<td <%if (i % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
												<br>
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<%
								}
							//空入力行の出力
							for (int j = dtHachuMeisaiList.size(); j < 9; j++) {
							%>
							<tr>
								<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<%=j + 1%>
								</td>
								<%-- 商品コード --%>
								<td align="center" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="hidden" name="syohin_cd_<%=j%>" value="">
									<input type="text" name="hachu_syohin_cd_<%=j%>" maxlength="13" value="" tabindex="-1" readonly>
								</td>
								<%-- 商品名 --%>
								<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="syohin_na_<%=j%>" value="" tabindex="-1" readonly />
									<input type="text" name="hi_kikaku_na_<%=j%>" value="" tabindex="-1" readonly />
								</td>
								<%-- 入数 --%>
								<td align="center" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="irisu_qt_<%=j%>" maxlength="4" value="" class="numeric" tabIndex="-1" readonly>
								</td>
								<%-- 発注単位 --%>
								<td rowspan="2" align="center" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<select name="hachu_tani_cd_<%=j%>" disabled>
										<option value=""></option>
										<%
											if (nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
											List selectList = nohinTaniList.getBeanList();
											for (int l = 0; l < selectList.size(); l++) {
												MaHachuTaniBean bean = (MaHachuTaniBean) selectList.get(l);
												String hachu_tani_cd = bean.getHachuTaniCd();
												String selected = "";

												if (searchData.getParameter("hachu_tani_cd") != null && hachu_tani_cd != null
												&& dtHachuMeisaiBean.getHachuTaniCd().equals(hachu_tani_cd)) {
											selected = "selected";
												}
										%>
										<option value="<%=hachu_tani_cd%>" <%=selected%>><%=bean.getHachuTaniNa()%>
										</option>
										<%
											}
										}
										%>
									</select>
								</td>
								<%-- 納品数 --%>
								<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="hachu_qt_<%=j%>" maxlength="4" value="" class="numeric" tabindex="-1" readonly />
								</td>
								<%-- 納品数量 --%>
								<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="hachu_suryo_qt_<%=j%>" maxlength="9" value="" class="numeric" tabindex="-1" readonly />
								</td>
								<%-- 原単価 --%>
								<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="gentanka_vl_<%=j%>" maxlength="10" value="" class="numeric" tabindex="-1" readonly />
								</td>
								<%-- 出庫単価 --%>
								<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<%
										// 取引先は非表示
									if (RoleUtil.isTorihikisakiFurumai(role)) {
									%>
									<input type="hidden" name="shukatanka_vl_<%=j%>" value="" tabindex="-1" disabled readonly>
									<%
										} else {
									%>
									<input type="text" name="shukatanka_vl_<%=j%>" maxlength="10" value="" class="numeric" tabindex="-1" readonly />
									<%
										}
									%>
								</td>
								<%-- 売単価 --%>
								<td align="center" rowspan="2" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<input type="text" name="baitanka_vl_<%=j%>" maxlength="7" value="" class="numeric" tabindex="-1" readonly />
								</td>
							</tr>
							<tr>
								<td colspan="3" <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
									<table border="0">
										<tr>
											<%-- 産地 --%>
											<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
												<input type="hidden" name="santi_cd_<%=j%>" value="" />
												<input type="text" name="santi_na_<%=j%>" value="" tabindex="-1" readonly />
											</td>
											<%-- 等級 --%>
											<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
												<input type="hidden" name="tokaikyu_cd_<%=j%>" value="" />
												<input type="text" name="tokaikyu_na_<%=j%>" value="" tabindex="-1" readonly />
											</td>
											<%-- 規格 --%>
											<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
												<input type="hidden" name="kikaku_cd_<%=j%>" value="" />
												<input type="text" name="kikaku_na_<%=j%>" value="" tabindex="-1" readonly />
											</td>
											<td <%if (j % 2 == 0) {%> style="background-color: #FFFFCF" <%}%>>
												<br>
											</td>
										</tr>
									</table>
								</td>
							</tr>
							<%
								}
							%>
						</table>
					</div>
					<div align="center">
						<font color="#0000FF"> ※定貫商品は、入力された納品数を元に算出した納品数量で、登録されます。 </font>
					</div>
				</td>
			</tr>
		</table>
		<br>
		<table align="center" border="0" cellspacing="1" cellpadding="0" class="data">
			<tr>
				<th align="center">納品数合計</th>
				<td>
					<input type="text" name="hachu_kei_qt" value="<%=HTMLUtil.toText(hachuKeiQt.longValue(), "#,##0")%>" class="numeric" tabindex="-1" readonly>
				</td>
				<th align="center">納品数量合計</th>
				<td>
					<input type="text" name="hachu_suryo_kei_qt" value="<%=HTMLUtil.toText(hachuSuryoKeiQt.doubleValue(), "#,##0.##")%>" class="numeric" tabindex="-1" readonly>
				</td>
				<th align="center">原価金額合計</th>
				<td>
					<input type="text" name="genka_kei_qt" value="<%=HTMLUtil.toText(genkaKeiQt.longValue(), "###,###,##0")%>" class="numeric" tabindex="-1" readonly>
				</td>
				<%
					// 取引先は非表示
				if (!RoleUtil.isTorihikisakiFurumai(role)) {
				%>
				<th align="center">出庫金額合計</th>
				<td>
					<input type="text" name="ido_genka_kei_qt" value="<%=HTMLUtil.toText(idoGenkaKeiQt.longValue(), "###,###,##0")%>" class="numeric" tabindex="-1" readonly>
				</td>
				<%
					}
				%>
				<th align="center">売価金額合計</th>
				<td>
					<input type="text" name="baika_kei_qt" value="<%=HTMLUtil.toText(baikaKeiQt.longValue(), "###,###,##0")%>" class="numeric" tabindex="-1" readonly>
				</td>
			</tr>
		</table>
		<%
			}
		%>
		<br>
		<table border="0" cellspacing="0" cellpadding="0">
			<tr>
				<td align="left">
					<input type="button" value="&nbsp;緊急発注登録一覧&nbsp;" class="btn" onClick="doBackTran();" />
				</td>
				<td align="right">
					<%
						if (emergencyTorokuStatus.getDisplayMode().equals("Insert")) {
					%>
					<input type="button" value="&nbsp;登&emsp;録&nbsp;" name="" class="btn" onClick="doInsertTran();">
					<%
						if (RoleUtil.isTorihikisakiFurumai(role)) {
					%>
					<%
						if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
							|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
							|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
							|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {
					%>
					<input type="button" value="全クリア" name="" class="btn" onClick="doAllCrearTran();" <%=buttonDis%>>
					<%
						}
					%>
					<%
						} else {
					%>
					<input type="button" value="全クリア" name="" class="btn" onClick="doAllCrearTran();" <%=buttonDis%>>
					<%
						}
					%>
					<%
						} else if (emergencyTorokuStatus.getDisplayMode().equals("Update")) {
					%>
					<input type="button" value="&nbsp;更&emsp;新&nbsp;" name="" class="btn" onClick="doUpdateTran();">
					<%
						if (RoleUtil.isTorihikisakiFurumai(role)) {
					%>
					<%
						if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
							|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
							|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
							|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {
					%>
					<input type="button" value="&nbsp;コピー&nbsp;" name="" class="btn" onClick="doCopyTran();">
					<input type="button" value="全クリア" name="" class="btn" onClick="doAllCrearTran();" <%=buttonDis%>>
					<%
						}
					%>
					<%
						} else {
					%>
					<input type="button" value="&nbsp;コピー&nbsp;" name="" class="btn" onClick="doCopyTran();">
					<input type="button" value="全クリア" name="" class="btn" onClick="doAllCrearTran();" <%=buttonDis%>>
					<%
						}
					%>
					<%
						} else if (emergencyTorokuStatus.getDisplayMode().equals("Reference")) {
					%>
					<%
						if (RoleUtil.isTorihikisakiFurumai(role)) {
					%>
					<%
						if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
							|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
							|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
							|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {
					%>
					<input type="button" value="&nbsp;コピー&nbsp;" name="" class="btn" onClick="doCopyTran();">
					<input type="button" value="全クリア" name="" class="btn" onClick="doAllCrearTran();" <%=buttonDis%>>
					<%
						}
					%>
					<%
						} else {
					%>
					<input type="button" value="&nbsp;コピー&nbsp;" name="" class="btn" onClick="doCopyTran();">
					<input type="button" value="全クリア" name="" class="btn" onClick="doAllCrearTran();" <%=buttonDis%>>
					<%
						}
					%>
					<%
						}
					%>
				</td>
				</div>
				</div>
				</td>
			</tr>
		</table>
		<input type="hidden" name="Modified" value="">
		<input type="hidden" name="ModifiedCondition" value="">
		<input type="hidden" name="deleteLine" />
		<input type="hidden" name="ikkatuShiireFg_" value="">
	</form>
</body>