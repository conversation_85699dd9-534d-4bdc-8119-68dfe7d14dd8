<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">
<!------ システム共通ヘッダー  START ------>
	<%if(title.equals("商品情報コメント登録")){%>
		<jsp:include page="ptl000001_Header.jsp?PARAM=商品情報コメント登録（SSN02004）"></jsp:include>
	<%
	}else{
	%>
		<jsp:include page="ptl000001_Header.jsp?PARAM=商品情報コメント追加登録（SSN02004）"></jsp:include>
	<%}%>
   	<tr height="5"></tr>    	
<!------ システム共通ヘッダー  END ------>
<!------ Body START ------>
 <tr>
   	<td align="center" valign="top">
	<form name="MainForm" method="post" action="app">
	<input type="hidden" name="ModifiedCondition" value=""/><%-- 検索条件の変更フラグ --%>
	<input type="hidden" name="Modified" value=""/><%-- 明細更新フラグ --%>
	<input type="hidden" name="userKb" value="<%=userKb%>">
	<input type="hidden" name="line" value=""/>
	<input type="hidden" name="SobaKb" value="<%=sobaKb%>"/>
	<input type="hidden" name="Url_ID" value="<%=HTMLUtil.toText(commentHolder.getParameter("Url_ID"))%>"/>
	<input type="hidden" name="tuban" value="<%=HTMLUtil.toText(commentHolder.getParameter("tuban"))%>"/>
		<jsp:include page="rbs00000_common.jsp" flush="true" />
		<%if(sobaKb != null && sobaKb.equals(SobaKb.SOBA.getCode())){%>
		<table border="0" cellspacing="1" cellpadding="0">
		<tr>
			<td colspan="4">
            <table border="0" width="100%" cellspacing="1" cellpadding="0" class="kensaku">
            <tr>
			<th nowrap>取引先</th>
			<td nowrap>
			<%if(!Url_ID.equals("retailMessageSubMenu")){%>
				<input type="text" name="torihikisaki_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("torihikisaki_cd"))%>" size="12" id="no_input_text" tabindex="-1" readonly style="width: 70px;"/>
				<input type="text" name="torihikisaki_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("torihikisaki_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 160px;"/>
			<%}else{%>
				<input type="text" name="torihikisaki_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("torihikisaki_cd"))%>" maxlength="9" size="12" style="ime-mode:disabled; width: 70px;" />
				<input type="text" name="torihikisaki_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("torihikisaki_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 160px;"/>
				<img src="images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="supplierSelect('MainForm.torihikisaki_cd','MainForm.torihikisaki_na')"/>
				<img src="images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clearText('torihikisaki_cd','torihikisaki_na')"/>
			<%}%>
			</td>
			<th nowrap>部門</th>
			<td nowrap>
			<%if(!Url_ID.equals("supplierMessageSubMenu")){%>
				<input type="text" name="bunrui1_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("bunrui1_cd"))%>" size="4" id="no_input_text" tabindex="-1" readonly style="width: 30px;"/>
				<input type="text" name="bunrui1_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("bunrui1_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 160px;"/>
			<%}else{%>
				<input type="text" name="bunrui1_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("bunrui1_cd"))%>" maxlength="3" size="4" style="ime-mode:disabled; width: 30px;" />
				<input type="text" name="bunrui1_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("bunrui1_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 160px;"/>
				<img src="images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="sectionSubSearchPopUp('MainForm.bunrui1_cd','MainForm.bunrui1_na')"/>
				<img src="images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clearText('bunrui1_cd','bunrui1_na')"/>
			<%}%>
			</td>
			</tr>
			</table>
            </td>
		</tr>
		<tr>
			<td colspan="4">
           	<table border="0" width="100%" cellspacing="1" cellpadding="0" class="kensaku">
           	<tr>
			<th nowrap>商品</th>
			<td nowrap>
			<%if(Url_ID.equals("syohinComment") || Url_ID.equals("orderFreshBuyer") || 
				Url_ID.equals("marketRegistration")|| Url_ID.equals("M2System")){%>
				<input type="text" name="syohin_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("syohin_cd"))%>" size="18" id="no_input_text" tabindex="-1" readonly style="width: 100px;"/>
				<input type="text" name="syohin_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("syohin_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 160px;"/>
				<input type="hidden" name="ma_syohin_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("ma_syohin_cd"))%>"/>
			<%}else{%>
				<input type="text" name="syohin_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("syohin_cd"))%>" maxlength="13" size="18" style="ime-mode:disabled; width: 100px;"/>
				<input type="text" name="syohin_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("syohin_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 160px;"/>
				<img src="images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"  onClick="syohinSelect()"/>
				<img src="images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clearText('syohin_cd','syohin_na')"/>
			<%}%>
			</td>
			</tr>
			</table>
            </td>
		</tr>
		<tr>
			<td colspan="4">
           	<table border="0" width="100%" cellspacing="1" cellpadding="0" class="kensaku">
           	<tr>
			<th nowrap>産地</th>
			<td nowrap>
				<input type="text" name="santi_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("santi_cd"))%>" size="8" id="no_input_text" tabindex="-1" readonly style="width: 50px;"/>
				<input type="text" name="santi_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("santi_na"))%>" size="20" id="no_input_text" tabindex="-1" readonly style="width: 110px;"/>
			</td>
			<th nowrap>等級</th>
			<td nowrap>
				<input type="text" name="tokaikyu_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("tokaikyu_cd"))%>" size="4" id="no_input_text" tabindex="-1" readonly style="width: 30px;"/>
				<input type="text" name="tokaikyu_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("tokaikyu_na"))%>" size="20" id="no_input_text" tabindex="-1" readonly style="width: 110px;"/>
			</td>
			<th nowrap>規格</th>
			<td nowrap>
				<input type="text" name="kikaku_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("kikaku_cd"))%>" size="4" id="no_input_text" tabindex="-1" readonly style="width: 30px;"/>
				<input type="text" name="kikaku_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("kikaku_na"))%>" size="20" id="no_input_text" tabindex="-1" readonly style="width: 110px;"/>
			</td>
			</tr>
			</table>
            </td>
		</tr>
		</table>
		<%}else{%>
		<table border="0" cellspacing="1" cellpadding="0" class="kensaku">
		<tr>
			<th nowrap>取引先</th>
			<td nowrap>
			<%if(!Url_ID.equals("retailMessageSubMenu")
				&& !(Url_ID.equals("M2System") && userKb.equals(UserSyubetu.KOURI.getCode()))){%>
				<input type="text" name="torihikisaki_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("torihikisaki_cd"))%>" size="12" id="no_input_text" tabindex="-1" readonly style="width: 100px;"/>
				<input type="text" name="torihikisaki_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("torihikisaki_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 160px;"/>
			<%}else{%>
				<input type="text" name="torihikisaki_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("torihikisaki_cd"))%>" maxlength="9" size="12" style="ime-mode:disabled; width: 100px;" />
				<input type="text" name="torihikisaki_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("torihikisaki_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 160px;"/>
				<img src="images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="supplierSelect('MainForm.torihikisaki_cd','MainForm.torihikisaki_na')"/>
				<img src="images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clearText('torihikisaki_cd','torihikisaki_na')"/>
			<%}%>
			</td>
			<th nowrap>部門</th>
			<td nowrap>
			<%if(!Url_ID.equals("supplierMessageSubMenu")){%>
				<input type="text" name="bunrui1_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("bunrui1_cd"))%>" size="4" id="no_input_text" tabindex="-1" readonly style="width: 50px;"/>
				<input type="text" name="bunrui1_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("bunrui1_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 110px;"/>
			<%}else{%>
				<input type="text" name="bunrui1_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("bunrui1_cd"))%>" maxlength="3" size="4" style="ime-mode:disabled; width: 50px;" />
				<input type="text" name="bunrui1_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("bunrui1_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 110px;"/>
				<img src="images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="sectionSubSearchPopUp('MainForm.bunrui1_cd','MainForm.bunrui1_na')"/>
				<img src="images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clearText('bunrui1_cd','bunrui1_na')"/>
			<%}%>
			</td>
		</tr>
		<tr>
			<th nowrap>商品</th>
			<td nowrap>
			<%if(Url_ID.equals("syohinComment") || Url_ID.equals("orderFreshBuyer") || 
				Url_ID.equals("marketRegistration")|| Url_ID.equals("M2System")){%>
				<input type="text" name="syohin_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("syohin_cd"))%>" size="18" id="no_input_text" tabindex="-1" readonly style="width: 30px;"/>
				<input type="text" name="syohin_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("syohin_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 100px;"/>
				<input type="hidden" name="ma_syohin_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("ma_syohin_cd"))%>"/>
			<%}else{%>
				<input type="text" name="syohin_cd" value="<%=HTMLUtil.toText(commentHolder.getParameter("syohin_cd"))%>" maxlength="13" size="18" style="ime-mode:disabled; width: 30px;" />
				<input type="text" name="syohin_na" value="<%=HTMLUtil.toText(commentHolder.getParameter("syohin_na"))%>" size="30" id="no_input_text" tabindex="-1" readonly style="width: 100px;"/>
				<img src="images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"  onClick="syohinSelect()"/>
				<img src="images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clearText('syohin_cd','syohin_na')"/>
			<%}%>
			</td>
		</tr>
		</table>
		<input type="hidden" name="santi_cd" value="000000"/>
		<input type="hidden" name="tokaikyu_cd" value="000"/>
		<input type="hidden" name="kikaku_cd" value="000"/>
		<%}%>

		<br>
		<br>
		<jsp:include page="InfoStringMdWare.jsp" />
		<br>
		<br>	
		<table border="0" cellspacing="1" cellpadding="0" class="data">
		<tr>
			<th nowrap width="100" style="text-align: left; padding-left: 15px; line-height: 14px;">掲載開始日</th>
			<th nowrap width="100" style="text-align: left; padding-left: 15px; line-height: 14px;">掲載終了日</th>
		</tr>
		<tr style="height: 22px;">
			<td align="center">
			<input type="text" name="keisai_fm_dt" size="15" value="<%=HTMLUtil.toText(commentHolder.getParameter("keisai_fm_dt"))%>" onBlur="dateFormalizer( this )" maxlength="8" style="ime-mode:disabled; width: 87px;" />
			<img src="images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar( document.MainForm, document.MainForm.keisai_fm_dt );"/>
			<small style="font-size: 8pt; vertical-align: bottom;">（YYYYMMDD）</small>
			</td>
			<td align="center">
			<input type="text" name="keisai_to_dt" size="15" value="<%=HTMLUtil.toText(commentHolder.getParameter("keisai_to_dt"))%>" onBlur="dateFormalizer( this )" maxlength="8" style="ime-mode:disabled; width: 87px;" />
			<img src="images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar( document.MainForm, document.MainForm.keisai_to_dt );"/>
			<small  style="font-size: 8pt; vertical-align: bottom;">（YYYYMMDD）</small>
			</td>
		</tr>
		<tr>
			<th nowrap width="400" colspan="2">コメント</th>
		</tr>
		<tr>
			<td  colspan="2" align="center" style="width: 446px; height: 36px; vertical-align: middle;">
				<textarea name="comment_tx" cols=60 rows=2 style="width: 438px; height: 28px; resize: none; overflow-y: scroll; "><%=HTMLUtil.toText(commentHolder.getParameter("comment_tx"))%></textarea>
			</td>
		</tr>
		</table>
		<br>
		<input type="button" name="commit" value="&emsp;登&emsp;録&emsp;" class="btn" onclick="doCommit()"/>
<%
		if(!Url_ID.equals("M2System")){
%>
		<input type="button" name="commit" value="&emsp;戻&emsp;る&emsp;" class="btn" onclick="goBack()"/>
<%
		}else if(Url_ID.equals("M2System")){
			UserBean bean = (UserBean)userSession.getBeanList().get(0);
			if(userKb.equals(UserSyubetu.TORIHIKI.getCode())){
%>
		<input type="button" name="commit" value="&emsp;戻&emsp;る&emsp;" class="btn" onclick="isM2ModifiedTran('S_comment_return','','','<%=bean.getRiyoUserId()%>','<%=bean.getPasswordKeyCd()%>','','id40')"/>
<%
			}else{
%>
		<input type="button" name="commit" value="&emsp;戻&emsp;る&emsp;" class="btn" onclick="isM2ModifiedTran('B_comment_return','<%=bean.getRiyoUserId()%>','<%=bean.getBunrui1Cd()%>','','','','id10')"/>
<%
			}
		}
%>
	</form>
	</td>
</tr>
<!---- Body END ---->

<!---- システム共通フッター START ---->
<!---- システム共通フッター END ---->
</table>
</body>