<body bgcolor="#FFFFFF" text="#000000" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0" onload="init();putOnLoadDisplay();">
<table width="100%" border="0" cellspacing="0" cellpadding="0" height="100%">
	<!------ システム共通ヘッダー  START ------>
	 <jsp:include page="ptl000001_Header.jsp?PARAM=緊急発注登録（店別）（SSN04006）"></jsp:include>
	<tr height="5"></tr>
	<!------ システム共通ヘッダー  END ------>
	<tr><td align="center" valign="top">
<!------ Body START ------>
<form name="MainForm" method="post" action="app">
<jsp:include page="rbs00000_common.jsp" />
<input type="hidden" name="Modified" value="">
<input type="hidden" name="ModifiedCondition" value="">
<input type="hidden" name="deleteLine" />
<input type="hidden" name="ikkatuShiireFg_" value="">

<!-- 原価チェック用 ---->
  <input type="hidden" name="GEN01_FUTOGO" value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getFutogo() : "" %>">
  <input type="hidden" name="GEN01_VALUE"  value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getValueString() : "" %>" >
  <input type="hidden" name="GEN01_MSG"    value="<%= wkGenkaCheck.get("GEN01") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN01")).getClMsg() : "" %>" >
  <input type="hidden" name="GEN02_FUTOGO" value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getFutogo() : "" %>">
  <input type="hidden" name="GEN02_VALUE"  value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getValueString() : "" %>" >
  <input type="hidden" name="GEN02_MSG"    value="<%= wkGenkaCheck.get("GEN02") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN02")).getClMsg() : "" %>" >
  <input type="hidden" name="GEN03_FUTOGO" value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getFutogo() : "" %>">
  <input type="hidden" name="GEN03_VALUE"  value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getValueString() : "" %>" >
  <input type="hidden" name="GEN03_MSG"    value="<%= wkGenkaCheck.get("GEN03") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN03")).getClMsg() : "" %>" >
  <input type="hidden" name="GEN04_FUTOGO" value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getFutogo() : "" %>">
  <input type="hidden" name="GEN04_VALUE"  value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getValueString() : "" %>" >
  <input type="hidden" name="GEN04_MSG"    value="<%= wkGenkaCheck.get("GEN04") != null ? ((WkGenkaCheckBean)wkGenkaCheck.get("GEN04")).getClMsg() : "" %>" >

<% if(emergencyTorokuStatus.getDisplayMode().equals("Insert")) {
//登録ヘッダ
%>

	<table border="0" cellspacing="0" cellpadding="0" class="kensaku" width="800">
		<tr>
			<th nowrap height="22">伝票番号</th>
			<td align="left" class="string_label" height="22" colspan="3" style="width: 288px;">※自動付与されます</td>
			<th nowrap width="80">*取引先</th>
			<td colspan="3" style="width: 341px;">
            <%
            //取引先権限振分
            if(RoleUtil.isTorihikisakiFurumai(role)){
            %>
              <input type="text" name="torihikisaki_cd" value="<% if(emergencyTorokuStatus.getTorihikisakiCd() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiCd())); %>" style="width: 50px;" size="8"  id="no_input_text" tabindex="-1" readOnly>
              <input type="text" name="torihikisaki_na" value="<% if(emergencyTorokuStatus.getTorihikisakiNa() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiNa())); %>" style="width: 210px;" size="40" id="no_input_text" tabindex="-1" readOnly>
            <%}else{%>
              <input type="text" name="torihikisaki_cd" value="<% if(emergencyTorokuStatus.getTorihikisakiCd() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiCd())); %>" size="8" maxlength="<%=torihikisakiCdLen %>"  <%= emergencyTorokuStatus.isTorihikisakiReadOnly()?" id=\"no_input_text\" readOnly style=\"width: 50px;\"":"style=\"width: 52px;\""%>>
              <input type="text" name="torihikisaki_na" value="<% if(emergencyTorokuStatus.getTorihikisakiNa() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiNa())); %>" size="25" id="no_input_text" tabindex="-1" readOnly style="width: 135px;">
              <%if(!emergencyTorokuStatus.isTorihikisakiReadOnly()){%>
                <img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_siireSel('MainForm.torihikisaki_cd', 'MainForm.torihikisaki_na');" />
                      <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="clear2(MainForm.torihikisaki_cd, MainForm.torihikisaki_na);" />
              <%}%>
            <%}%>
			</td>
		</tr>
		<tr>
			<th nowrap >*部門</th>
			<td nowrap colspan="3">
				<input type="text" name="bunrui1_cd" value="<% if(emergencyTorokuStatus.getBunrui1Cd() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getBunrui1Cd())); %>" size="8" maxlength="<%=bunrui1CdLen %>" style="ime-mode:disabled; width: 52px;" onChange="changeModifiedCondition()"/>
				<input type="text" name="bunrui1_na" value="<% if(emergencyTorokuStatus.getBunrui1Na() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getBunrui1Na())); %>" id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_DptLineClass('MainForm.bunrui1_cd', 'MainForm.bunrui1_na','','','','',1,MainForm.bunrui1_cd.value,MainForm.bunrui1_na.value);"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('bunrui1')"/>
			</td>
			<th nowrap >*相場区分</th>
			<td nowrap colspan="3">
				<%	String soba_selected = "";
					String hisoba_selected = "";
					if(emergencyTorokuStatus.getSobaKb() != null ){
						if(emergencyTorokuStatus.getSobaKb().equals(SobaKb.SOBA)){
							soba_selected = "selected";
						}else if(emergencyTorokuStatus.getSobaKb().equals(SobaKb.HISOBA)){
							hisoba_selected = "selected";
						}
					}
				%>
				<select name="soba_kb" onChange="changeModifiedCondition()">
					<option value="<%= HTMLUtil.toLabel(SobaKb.SOBA.getCode())%>"  <%=soba_selected%> ><%= HTMLUtil.toLabel(SobaKb.SOBA.toString())%></option>
				</select>
			</td>
		</tr>
		<tr>
			<th nowrap >*店舗</th>
			<td nowrap colspan="3">
				<input type="text" name="tenpo_cd" value="<% if(emergencyTorokuStatus.getTenpoCd() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getTenpoCd())); %>"  size="8" maxlength="<%=tenpoCdLen %>" style="ime-mode:disabled; width: 52px;" onChange="changeModifiedCondition()"/>
				<input type="text" name="tenpo_na" value="<% if(emergencyTorokuStatus.getTenpoNa() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getTenpoNa())); %>"  id="no_input_text" tabindex="-1" readonly style="width: 110px;" />
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択" onClick="pop_tenpoSel('MainForm.tenpo_cd', 'MainForm.tenpo_na', '');"/>
				<img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="javascript:clearCdNa('tenpo')"/>
			</td>
			<th nowrap>*納品区分</th>
			<td nowrap align="left">
				<select name="buturyu_kb" onChange="changeModifiedCondition(); changeIkkatuFg();">
					<option value=""></option>
<%
	if( maButuryuBh.getMaxRows() > 0 ) {
		for( Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext(); ) {
			MaButuryuBean bean = (MaButuryuBean)ite.next();
			if(!HTMLUtil.toText(bean.getShiwakeKb().trim()).equals("3")) { %>
				<% if (RoleUtil.isTorihikisakiFurumai(role)) {%>
					<% if (!bean.getButuryuKb().equals(ButuryuKb.TC_ICHIBA.getCode()) && !bean.getButuryuKb().equals(ButuryuKb.TYOKUNO_ICHIBA.getCode())){%>
				        <option value="<%= HTMLUtil.toText(bean.getButuryuKb()) %>"
				        <%= emergencyTorokuStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ?  " selected" : "" %>>
				        <%= HTMLUtil.toLabel(bean.getButuryuNm()) %></option>
				        <%}	%>
				<%}	else {%>
					<option value="<%= HTMLUtil.toText(bean.getButuryuKb()) %>"
				    <%= emergencyTorokuStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ?  " selected" : "" %>>
				    <%= HTMLUtil.toLabel(bean.getButuryuNm()) %></option>
				<%}	%>
<%			}	%>
<%		}	%>
<%	}	%>
				</select>
			</td>
			<th nowrap>経由センター</th>
			<td nowrap align="left">
				<select name="center_cd" onChange="changeModifiedCondition();">
					<option value=""></option>
<%
	if( maCenterBh.getMaxRows() > 0 ) {
		for( Iterator ite = maCenterBh.getBeanIterator(); ite.hasNext(); ) {
			MaCenterBean bean = (MaCenterBean)ite.next();  %>
			<option value="<%= HTMLUtil.toText(bean.getCenterCd()) %>"
		    <%= emergencyTorokuStatus.getCenterCd().trim().equals(bean.getCenterCd().trim()) ?  " selected" : "" %>>
		    <%= HTMLUtil.toLabel(bean.getCenterShortNa()) %></option>
<%		}	%>
<%	}	%>
				</select>
			</td>


		</tr>
	</table>
	<table border="0" cellspacing="0" cellpadding="0" class="kensaku" width="800">
		<tr>
			<th nowrap width="80">*発注日</th>
			<td nowrap width="291">
				<input type="text" name="hachu_dt" value="<% if(emergencyTorokuStatus.getHachuDt() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getHachuDt())); %>" size="10" maxlength="8" style="ime-mode:disabled; width: 62px;" onChange="changeModifiedCondition()"/>
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.hachu_dt);" />
				<small>（YYYYMMDD）</small>
			</td>
			<th nowrap >*納品日</th>
			<td nowrap >
				<input type="text" name="nohin_dt" value="<% if(emergencyTorokuStatus.getNohinDt() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getNohinDt())); %>" size="10" maxlength="8" style="ime-mode:disabled; width: 62px;" onChange="changeModifiedCondition()"/>
				<img src="./images/calendar.gif" width="18" height="18" align="absmiddle" alt="日付選択" onClick="callCalendar(MainForm, MainForm.nohin_dt);" />
				<small>（YYYYMMDD）</small>
			</td>
			<th nowrap>*便</th>
			<td nowrap align="left">
				<input type="text" name="bin_nm" size="1" maxlength="1" class="numeric" value="<%= HTMLUtil.toText(emergencyTorokuStatus.getBinNm()) %>" style="ime-mode:disabled; width: 17px;" onChange="changeModifiedCondition()"/>
			</td>
		</tr>
	</table>
<%
}else{
//参照ヘッダ
%>
	<table border="0" cellspacing="0" cellpadding="0" class="kensaku" width="800">
		<tr>
			<th nowrap height="22">伝票番号</th>
			<td align="left" class="string_label" height="22" colspan="3" style="width: 289px;"><%= HTMLUtil.toText(dtHachuBean.getDenpyoNb()) %></td>
			<th nowrap width="80">取引先</th>
			<td colspan="3" width="340">
				<input type="text" name="torihikisaki_cd" value="<% if(emergencyTorokuStatus.getTorihikisakiCd() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiCd())); %>" style="width: 50px;" size="8" id="no_input_text" tabindex="-1" readOnly />
				<input type="text" name="torihikisaki_na" value="<% if(emergencyTorokuStatus.getTorihikisakiNa() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getTorihikisakiNa())); %>" style="width: 110px;" size="20" id="no_input_text" tabindex="-1" readOnly>
			</td>
		</tr>
		<tr>
			<th nowrap >部門</th>
			<td nowrap colspan="3">
				<input type="text" name="bunrui1_cd" value="<% if(emergencyTorokuStatus.getBunrui1Cd() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getBunrui1Cd())); %>" style="width: 50px;" size="8" maxlength="<%=bunrui1CdLen %>" id="no_input_text" tabindex="-1" readOnly />
				<input type="text" name="bunrui1_na" value="<% if(emergencyTorokuStatus.getBunrui1Na() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getBunrui1Na())); %>" style="width: 110px;" id="no_input_text" tabindex="-1" readonly />
			</td>
			<th nowrap >相場区分</th>
			<td nowrap colspan="3">
				<%	String soba_selected = "";
					String hisoba_selected = "";
					if(emergencyTorokuStatus.getSobaKb() != null ){
						if(emergencyTorokuStatus.getSobaKb().equals(SobaKb.SOBA)){
							soba_selected = "selected";
						}else if(emergencyTorokuStatus.getSobaKb().equals(SobaKb.HISOBA)){
							hisoba_selected = "selected";
						}
					}
				%>
				<select name="soba_kb" disabled>
					<option value="<%= HTMLUtil.toLabel(SobaKb.SOBA.getCode())%>"  <%=soba_selected%> ><%= HTMLUtil.toLabel(SobaKb.SOBA.toString())%></option>
					<option value="<%= HTMLUtil.toLabel(SobaKb.HISOBA.getCode())%>" <%=hisoba_selected%>  ><%= HTMLUtil.toLabel(SobaKb.HISOBA.toString())%></option>
				</select>
			</td>
		</tr>
		<tr>
			<th nowrap >店舗</th>
			<td nowrap colspan="3">
				<input type="text" name="tenpo_cd" value="<% if(emergencyTorokuStatus.getTenpoCd() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getTenpoCd())); %>"  style="width: 50px;" size="8" maxlength="<%=tenpoCdLen %>" id="no_input_text" tabindex="-1" readOnly />
				<input type="text" name="tenpo_na" value="<% if(emergencyTorokuStatus.getTenpoNa() != null ) out.print(HTMLUtil.toText(emergencyTorokuStatus.getTenpoNa())); %>"  style="width: 110px;" id="no_input_text" tabindex="-1" readonly  />
			</td>
			<th nowrap>納品区分</th>
			<td nowrap align="left">
				<select name="buturyu_kb" disabled >
					<option value=""></option>
<%
	if( maButuryuBh.getMaxRows() > 0 ) {
		for( Iterator ite = maButuryuBh.getBeanIterator(); ite.hasNext(); ) {
			MaButuryuBean bean = (MaButuryuBean)ite.next();
			if(!HTMLUtil.toText(bean.getShiwakeKb().trim()).equals("3")) {%>
				<option value="<%= HTMLUtil.toText(bean.getButuryuKb()) %>"
				<%= emergencyTorokuStatus.getButuryuKb().trim().equals(bean.getButuryuKb().trim()) ?  " selected" : "" %>>
				<%= HTMLUtil.toLabel(bean.getButuryuNm()) %></option>
<%			}	%>
<%		}	%>
<%	}	%>
				</select>
			</td>
			<th nowrap>経由センター</th>
			<td nowrap align="left">
				<select name="center_cd" disabled >
					<option value=""></option>
<%
	if( maCenterBh.getMaxRows() > 0 ) {
		for( Iterator ite = maCenterBh.getBeanIterator(); ite.hasNext(); ) {
			MaCenterBean bean = (MaCenterBean)ite.next(); %>
				<option value="<%= HTMLUtil.toText(bean.getCenterCd()) %>"
				<%= emergencyTorokuStatus.getCenterCd().trim().equals(bean.getCenterCd().trim()) ?  " selected" : "" %>>
				<%= HTMLUtil.toLabel(bean.getCenterShortNa()) %></option>
<%		}	%>
<%	}	%>
				</select>
			</td>
		</tr>
	</table>

	<table border="0" cellspacing="0" cellpadding="0" class="kensaku" width="800">
		<tr>
			<th nowrap width="80">発注日</th>
			<td nowrap width="292">
				<input type="hidden" name="hachu_dt" value="<%=emergencyTorokuStatus.getHachuDt()%>">
				<input type="text" name="hachu_dt_display" value="<% if(emergencyTorokuStatus.getHachuDt() != null ) out.print(HTMLUtil.toDate(emergencyTorokuStatus.getHachuDt(),"yyyy/MM/dd")); %>" size="13" style="width: 75px;" id="no_input_text" tabindex="-1" readOnly />
				<small>（YYYYMMDD）</small>
			</td>
			<th nowrap >納品日</th>
			<td nowrap >
				<input type="hidden" name="nohin_dt" value="<%=emergencyTorokuStatus.getNohinDt()%>">
				<input type="text" name="nohin_dt_display" value="<% if(emergencyTorokuStatus.getNohinDt() != null ) out.print(HTMLUtil.toDate(emergencyTorokuStatus.getNohinDt(),"yyyy/MM/dd")); %>" size="13" style="width: 75px;" id="no_input_text" tabindex="-1" readOnly />
				<small>（YYYYMMDD）</small>
			</td>
			<th nowrap>便</th>
			<td nowrap align="left">
				<input type="text" name="bin_nm" size="1" style="width: 15px;" maxlength="1" class="numeric" value="<%= HTMLUtil.toText(emergencyTorokuStatus.getBinNm()) %>" id="no_input_text" tabindex="-1" readOnly />
			</td>
		</tr>
	</table>

<%
}
%>
	<br />

	<jsp:include page="InfoStringMdWare.jsp" />

	<br />

<% if(!emergencyTorokuStatus.getDisplayMode().equals("Reference")) {
//登録明細
%>
	<table width="750" border="0" cellspacing="0" cellpadding="0" >
		<tr>
			<td align="center">
				<input type="button" value="明細情報取得"  name="" class="btn" onClick="doSearchTran();" style="width: 116px;">
			</td>
	</table>

	<br />


<table>
<tr align = "left">
<td>

	<div align="left" style="overflow-x:hidden; width: 980px; height: 530px;">
	<table border="0" cellspacing="1" cellpadding="0" class="data" style="line-height: 14px;">
	    <tr>
			<th nowrap width="20" rowspan="2" >No</th>
			<th nowrap width="100" align="center">商品コード</th>
			<th nowrap width="324" align="center">商品名</th>
			<th nowrap width="41" align="center">入数</th>
			<th nowrap width="68" align="center"  rowspan="2" >発注単位</th>
			<th nowrap width="55" align="center"  rowspan="2">納品数</th>
			<th nowrap width="80" align="center"  rowspan="2">納品数量</th>
			<th nowrap width="80" align="center"  rowspan="2">原単価</th>
			<%// 取引先は非表示
			if (RoleUtil.isTorihikisakiFurumai(role)) { %>
				<th nowrap width="80" align="center"  rowspan="2"></th>
			<%} else { %>
				<th nowrap width="80" align="center"  rowspan="2">出庫単価</th>
			<%} %>
			<th nowrap width="70" align="center"  rowspan="2">売単価</th>
			<%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
			    <%	if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				        || emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    	|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    	|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
			                <th nowrap width="45" align="center"  rowspan="2">クリア</th>
				<%	}	%>
		    <%	} else {	%>
			        <th nowrap width="45" align="center"  rowspan="2">クリア</th>
			<%	} %>
	    </tr>
		<tr>
			<th nowrap colspan="3">
		      <table border="0" cellspacing="1" cellpadding="0" class="data" width="100%">
		        <tr>
		          <th align="center">産地</th>
		          <th align="center">等級</th>
		          <th align="center">規格</th>
		        </tr>
		      </table>
		    </th>
		</tr>

	<%
		DtHachuMeisaiBean dtHachuMeisaiBean = null;

		BigDecimal hachuKeiQt 		= new BigDecimal("0");
		BigDecimal hachuSuryoKeiQt 	= new BigDecimal("0");
		BigDecimal genkaKeiQt 		= new BigDecimal("0");
		BigDecimal idoGenkaKeiQt 	= new BigDecimal("0");
		BigDecimal baikaKeiQt 		= new BigDecimal("0");

		for(int i = 0; i < dtHachuMeisaiList.size(); i++){
			dtHachuMeisaiBean = (DtHachuMeisaiBean)dtHachuMeisaiList.get(i);
			hachuKeiQt 		= hachuKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getHachuQtString()));
			hachuSuryoKeiQt = hachuSuryoKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getHachuSuryoQtString()));
			genkaKeiQt 		= genkaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getGenkaVlString()));
			idoGenkaKeiQt	= idoGenkaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getIdoGenkaVlString()));
			baikaKeiQt		= baikaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getBaikaVlString()));
	%>

		<tr height="29" >
			<td nowrap width="20" align="center" rowspan="2" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<%=i+1%>
			</td>

			<%-- 商品コード --%>
			<td nowrap width="100" align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="hidden" name="syohin_cd_<%=i%>" value="<% if(dtHachuMeisaiBean.getSyohinCd() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSyohinCd()));%>">
				<input type="text" name="hachu_syohin_cd_<%=i%>" maxlength="13" size="16" value="<% if(dtHachuMeisaiBean.getHachuSyohinCd() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getHachuSyohinCd()));%>" style="ime-mode:disabled; width: 90px;" onChange="changeModifiedCondition()" >
			</td>

			<%-- 商品名称 --%>
			<td nowrap width="320" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="syohin_na_<%=i%>" size="30" style="width: 160px;" maxlength="40" value="<% if(dtHachuMeisaiBean.getSyohinNa() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSyohinNa()));%>" />
				<input type="text" name="hi_kikaku_na_<%=i%>" size="18" style="width: 100px;"  value="<% if(dtHachuMeisaiBean.getHiKikakuNa() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getHiKikakuNa()));%>" id="no_input_text" tabindex="-1" readonly/>
				<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"
					onClick="if(checkBunrui1())if(checkSobaKb())
						pop_syohinSel(
						'MainForm.hachu_syohin_cd_<%=i%>',
						'MainForm.syohin_na_<%=i%>',
						'MainForm.hi_kikaku_na_<%=i%>',
						MainForm.bunrui1_cd,
						'','',
						document.MainForm.soba_kb.value,
						'',
					    document.MainForm.nohin_dt
					);"
				/>
			</td>

			<%-- 入数 --%>
			<td nowrap width="45" align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="irisu_qt_<%=i%>" maxlength="4" size="4" style="width: 32px;" value="<%= HTMLUtil.toLabel(dtHachuMeisaiBean.getIrisuQtString(), "###0") %>" class="numeric"/>
			</td>
			<%-- 発注単位 --%>
			<td nowrap rowspan="2" width="68" align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<select name="hachu_tani_cd_<%=i%>" size="1" style="width: 60px;" onChange="changeModifiedCondition()">
					<option value=""></option>
				<%
				   if(nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
					List selectList = nohinTaniList.getBeanList();
					  for(int k = 0;k < selectList.size(); k++ ) {
					  	MaHachuTaniBean bean = (MaHachuTaniBean)selectList.get(k);
					  	String hachu_tani_cd = bean.getHachuTaniCd();
					  	String selected = "";

					  	if(hachu_tani_cd != null && dtHachuMeisaiBean.getHachuTaniCd().equals(hachu_tani_cd)){
					  	   selected = "selected";
					  	}
				%>
						<option value="<%=hachu_tani_cd%>"<%=selected%>><%=bean.getHachuTaniNa()%>
						</option>
				<%
					  }
				   }
				%>
				</select>
			</td>

			<%-- 納品数 --%>
			<td nowrap width="55"  align="center" rowspan="2" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="hachu_qt_<%=i%>" maxlength="4" size="6" value="<% if( !(dtHachuMeisaiBean.getHachuQt() == 0 && dtHachuMeisaiBean.getHachuSuryoQt() == 0) ) out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getHachuQtString(), "##0")); %>" class="numeric" style="ime-mode:disabled; width: 42px;" onChange="changeModifiedCondition();">
			</td>

			<%-- 納品数量 --%>
			<td nowrap width="80"  align="center" rowspan="2" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="hachu_suryo_qt_<%=i%>" maxlength="9" size="11" value="<% if(dtHachuMeisaiBean.getHachuSuryoQt() != 0 ) out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getHachuSuryoQtString(), "####0.##")); %>"  class="numeric" style="ime-mode:disabled; width: 67px;" onChange="changeModifiedCondition();">
			</td>

			<%-- 原単価 --%>
			<td nowrap width="80"  align="center" rowspan="2" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="gentanka_vl_<%=i%>" maxlength="10" size="11" value="<% if(dtHachuMeisaiBean.getKakuGentankaVl() != 0 ) out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getKakuGentankaVlString(), "######0.00")); %>" class="numeric" style="ime-mode:disabled; width: 67px;" onChange="changeModifiedCondition();"/>
			</td>

			<%-- 出庫単価 --%>
			<td nowrap width="80"  align="center" rowspan="2" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<%
				// 取引先は非表示
				if(RoleUtil.isTorihikisakiFurumai(role)){
				%>
					<input type="hidden" name="shukatanka_vl_<%=i%>" value="<% if(dtHachuMeisaiBean.getIdoGentankaVl() != 0 ) out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getIdoGentankaVlString(), "######0.00")); %>" />
		    	<%}else{%>
					<input type="text" name="shukatanka_vl_<%=i%>" maxlength="10" size="11" value="<% if(dtHachuMeisaiBean.getIdoGentankaVl() != 0 ) out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getIdoGentankaVlString(), "######0.00")); %>" class="numeric" style="ime-mode:disabled; width: 67px;" onChange="changeModifiedCondition()"/>
				<%}%>
			</td>

			<%-- 売単価 --%>
			<td nowrap width="70"  align="center" rowspan="2" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="baitanka_vl_<%=i%>" maxlength="7" size="9" value="<% if(dtHachuMeisaiBean.getKakuBaitankaVl() != 0 ) out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getKakuBaitankaVlString(), "#######0")); %>" class="numeric" style="ime-mode:disabled; width: 57px;" onChange="changeModifiedCondition();"/>
			</td>

			<%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
			    <%	if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				        || emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    	|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    	|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
			                <td nowrap  width="45" align="center" rowspan="2" rowspan="2" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				            <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="meisaiClear(<%=i%>);"/>
			                </td>
				<%	}	%>
		    <%	} else {	%>
			         <td nowrap  width="45" align="center" rowspan="2" rowspan="2" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				     <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="meisaiClear(<%=i%>);"/>
			         </td>
			<%	} %>
		</tr>
		<tr>
			<td nowrap colspan="3" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
		      <table border="0" cellspacing="1" cellpadding="0"  width="100%">
		        <tr>

					<%-- 産地 --%>
					<td nowrap <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="santi_cd_<%=i%>" value="<% if(dtHachuMeisaiBean.getSantiCd() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSantiCd()));%>" />
						<input type="text" name="santi_na_<%=i%>" size="16" style="width: 90px;" value="<% if(dtHachuMeisaiBean.getSantiNa() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSantiNa()));%>" id="no_input_text" tabindex="-1" readonly onChange="changeModifiedCondition()"/>
						<img src="./images/b_select_w.png" width="38" height="20" align="absmiddle" alt="選択"
							onClick="if(checkTorihikisaki()) pop_santiSel('MainForm.santi_cd_<%=i%>','MainForm.santi_na_<%=i%>',MainForm.torihikisaki_cd.value);"/>
					</td>

					<%-- 等級 --%>
					<td nowrap <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="tokaikyu_cd_<%=i%>" value="<% if(dtHachuMeisaiBean.getTokaikyuCd() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getTokaikyuCd()));%>" />
						<input type="text" name="tokaikyu_na_<%=i%>" size="16" style="width: 90px;" value="<% if(dtHachuMeisaiBean.getTokaikyuNa() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getTokaikyuNa()));%>" id="no_input_text" tabindex="-1" readonly onChange="changeModifiedCondition()"/>
						<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"
							onClick="if(checkTorihikisaki()) pop_tokaiqSel('MainForm.tokaikyu_cd_<%=i%>','MainForm.tokaikyu_na_<%=i%>',MainForm.torihikisaki_cd.value);"/>
					</td>

					<%-- 規格 --%>
					<td nowrap <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="kikaku_cd_<%=i%>" value="<% if(dtHachuMeisaiBean.getKikakuCd() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getKikakuCd()));%>" />
						<input type="text" name="kikaku_na_<%=i%>" size="16" style="width: 90px;" value="<% if(dtHachuMeisaiBean.getKikakuNa() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getKikakuNa()));%>" id="no_input_text" tabindex="-1" readonly onChange="changeModifiedCondition()"/>
						<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"
							onClick="if(checkTorihikisaki()) pop_kikakuSel('MainForm.kikaku_cd_<%=i%>','MainForm.kikaku_na_<%=i%>',MainForm.torihikisaki_cd.value);"/>
					</td>
			        <%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
			            <%	if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				                || emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    	        || emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    	        || emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
					                <td <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
						            <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="STKClear(<%=i%>);"/>
					                </td>
                       <%	}	%>
		            <%	} else {	%>
					       <td <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
						   <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="STKClear(<%=i%>);"/>
					       </td>
			        <%	} %>
		        </tr>
		      </table>
			</td>
		</tr>
	<%
		}

		for ( int j = dtHachuMeisaiList.size(); j < 9; j++ ) {
	%>
		<tr height="29">
			<td nowrap width="20" height="40" align="center" rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<%=j+1%>
			</td>

			<%	if (RoleUtil.isTorihikisakiFurumai(role)
			        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
			        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
					<td nowrap width="100" align="center" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="syohin_cd_<%=j%>" value="">
						<input type="text" name="hachu_syohin_cd_<%=j%>" maxlength="13" size="16" value="" style="ime-mode:disabled; width: 90px;" id="no_input_text" tabindex="-1" readonly>
					</td>
		    <%	} else {	%>
					<td nowrap width="100" align="center"  <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="syohin_cd_<%=j%>" value="">
						<input type="text" name="hachu_syohin_cd_<%=j%>" maxlength="13" size="16" value="" style="ime-mode:disabled; width: 90px;" onChange="changeModifiedCondition()">
					</td>
			<%	} %>

			<%	if (RoleUtil.isTorihikisakiFurumai(role)
			        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
			        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
					<td nowrap width="320" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="text" name="syohin_na_<%=j%>" style="width: 160px;" size="30" value="" id="no_input_text" tabindex="-1" readonly/>
						<input type="text" name="hi_kikaku_na_<%=j%>" style="width: 140px;" size="26" value="" id="no_input_text" tabindex="-1" readonly/>
					</td>
		    <%	} else {	%>
					<td nowrap width="320" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="text" name="syohin_na_<%=j%>" size="30" style="width: 162px;" maxlength="40" value="" />
						<input type="text" name="hi_kikaku_na_<%=j%>" size="18" style="width: 100px;" value="" id="no_input_text" tabindex="-1" readonly/>
						<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"
							onClick="if(checkBunrui1())if(checkSobaKb())
								pop_syohinSel(
								'MainForm.hachu_syohin_cd_<%=j%>',
								'MainForm.syohin_na_<%=j%>',
								'MainForm.hi_kikaku_na_<%=j%>',
								MainForm.bunrui1_cd,
								'','',
								document.MainForm.soba_kb.value,
								'',
							    document.MainForm.nohin_dt
							);"
						/>
					</td>
			<%	} %>

			<%	if (RoleUtil.isTorihikisakiFurumai(role)
			        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
			        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
					<td nowrap width="45" align="center" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="text" name="irisu_qt_<%=j%>" maxlength="4" size="4" style="width: 30px;" value="" id="no_input_text"  class="numeric" tabIndex="-1" readonly>
					</td>
		    <%	} else {	%>
					<td nowrap width="45" align="center" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="text" name="irisu_qt_<%=j%>" maxlength="4" size="5" style="width: 37px;" value="" class="numeric"/>
					</td>
			<%	} %>

			<%	if (RoleUtil.isTorihikisakiFurumai(role)
			        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
			        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
					<td nowrap rowspan="2" width="68" align="center" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<select name="hachu_tani_cd_<%=j%>" size="1" style="width: 60px;" disabled>
							<option value=""></option>
						<%
						   if(nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
							List selectList = nohinTaniList.getBeanList();
							  for(int l = 0;l < selectList.size(); l ++ ) {
							  	MaHachuTaniBean bean = (MaHachuTaniBean)selectList.get(l);
							  	String hachu_tani_cd = bean.getHachuTaniCd();
							  	String selected = "";

							  	if(searchData.getParameter("hachu_tani_cd") != null && hachu_tani_cd != null && dtHachuMeisaiBean.getHachuTaniCd().equals(hachu_tani_cd)){
							  	   selected = "selected";
							  	}
						%>
								<option value="<%=hachu_tani_cd%>"<%=selected%>><%=bean.getHachuTaniNa()%>
								</option>
						<%
							  }
					   }

					%>
					</select>

				</td>
		    <%	} else {	%>
					<td nowrap rowspan="2" width="68" align="center" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>				<select name="hachu_tani_cd_<%=j%>" size="1" style="width: 60px;" onChange="changeModifiedCondition()">
							<option value=""></option>
						<%
						   if(nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
							List selectList = nohinTaniList.getBeanList();
							  for(int l = 0;l < selectList.size(); l ++ ) {
							  	MaHachuTaniBean bean = (MaHachuTaniBean)selectList.get(l);
							  	String hachu_tani_cd = bean.getHachuTaniCd();
							  	String selected = "";

							  	if(searchData.getParameter("hachu_tani_cd") != null && hachu_tani_cd != null && dtHachuMeisaiBean.getHachuTaniCd().equals(hachu_tani_cd)){
							  	   selected = "selected";
							  	}
						%>
								<option value="<%=hachu_tani_cd%>"<%=selected%>><%=bean.getHachuTaniNa()%>
								</option>
						<%
							  }
						   }
						%>
						</select>

					</td>
			<%	} %>

			<%	if (RoleUtil.isTorihikisakiFurumai(role)
			        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
			        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
			        <td nowrap width="55"  align="center" rowspan="2"  <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				        <input type="text" name="hachu_qt_<%=j%>" maxlength="4" size="6" value="" class="numeric" style="ime-mode:disabled; width: 40px;" id="no_input_text" tabindex="-1" readonly/>
			        </td>
		    <%	} else {	%>
			        <td nowrap  width="55"  align="center" rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				        <input type="text" name="hachu_qt_<%=j%>" maxlength="4" size="6" value="" class="numeric" style="ime-mode:disabled; width: 42px;" onChange="changeModifiedCondition();">
			        </td>
			<%	} %>

			<%	if (RoleUtil.isTorihikisakiFurumai(role)
			        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
			        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
			        <td nowrap width="80"  align="center" rowspan="2"  <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				        <input type="text" name="hachu_suryo_qt_<%=j%>" maxlength="9" size="11" value=""  class="numeric" style="ime-mode:disabled; width: 65px;" id="no_input_text" tabindex="-1" readonly/>
			        </td>
		    <%	} else {	%>
			        <td nowrap  width="80"  align="center" rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				        <input type="text" name="hachu_suryo_qt_<%=j%>" maxlength="9" size="11" value=""  class="numeric" style="ime-mode:disabled; width: 65px;" onChange="changeModifiedCondition();">
			        </td>
			<%	} %>

			<%	if (RoleUtil.isTorihikisakiFurumai(role)
			        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
			        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
			        <td nowrap width="80"  align="center" rowspan="2"  <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				        <input type="text" name="gentanka_vl_<%=j%>" maxlength="10" size="11" value="" class="numeric" style="ime-mode:disabled; width: 65px;" id="no_input_text" tabindex="-1" readonly/>
			        </td>
		    <%	} else {	%>
			        <td nowrap  width="80"  align="center" rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				        <input type="text" name="gentanka_vl_<%=j%>" maxlength="10" size="11" value="" class="numeric" style="ime-mode:disabled; width: 65px;" onChange="changeModifiedCondition();"/>
			        </td>
			<%	} %>

			<%-- 出庫単価 --%>
			<td nowrap  width="80"  align="center" rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<%
				// 取引先は非表示
				if(RoleUtil.isTorihikisakiFurumai(role)){
				%>
					<input type="hidden" name="shukatanka_vl_<%=j%>" value="" tabindex="-1" disabled readonly>
		    	<%}else{%>
					<input type="text" name="shukatanka_vl_<%=j%>" maxlength="10" size="11" value="" class="numeric" style="ime-mode:disabled; width: 65px;" onChange="changeModifiedCondition()"/>
				<%}%>
			</td>

			<%	if (RoleUtil.isTorihikisakiFurumai(role)
			        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
			        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
			        <td nowrap width="70"  align="center" rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				        <input type="text" name="baitanka_vl_<%=j%>" maxlength="7" size="9" value="" class="numeric" style="ime-mode:disabled; width: 55px;" id="no_input_text" tabindex="-1" readonly/>
			        </td>
		    <%	} else {	%>
			        <td nowrap  width="70"  align="center" rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				        <input 	type="text" name="baitanka_vl_<%=j%>" maxlength="7" size="9" value="" class="numeric" style="ime-mode:disabled; width: 55px;" onChange="changeModifiedCondition();"/>
			        </td>
			<%	} %>

			<%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
			    <%	if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				        || emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    	|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    	|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
			                <td nowrap width="45"  rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				            <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="meisaiClear(<%=j%>);"/>
			                </td>
                <%	}	%>
		    <%	} else {	%>
			        <td nowrap width="45"  rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
			        <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="meisaiClear(<%=j%>);"/>
			        </td>
			<%	} %>
		</tr>
		<tr>
			<td nowrap colspan="3" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
		      <table border="0" cellspacing="1" cellpadding="0"  width="100%">
		        <tr>
					<%	if (RoleUtil.isTorihikisakiFurumai(role)
					        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
					        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
						    <td nowrap <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
							    <input type="hidden" name="santi_cd_<%=j%>" value="" />
							    <input type="text" name="santi_na_<%=j%>" size="27" style="width: 145px;" value="" id="no_input_text" tabindex="-1" readonly/>
						    </td>
				    <%	} else {	%>
							<td nowrap <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
								<input type="hidden" name="santi_cd_<%=j%>" value="" />
								<input type="text" name="santi_na_<%=j%>" size="16" style="width: 90px;" value="" id="no_input_text" tabindex="-1" readonly onChange="changeModifiedCondition()"/>
								<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"
									onClick="if(checkTorihikisaki()) pop_santiSel('MainForm.santi_cd_<%=j%>','MainForm.santi_na_<%=j%>',MainForm.torihikisaki_cd.value);"/>
							</td>
					<%	} %>

					<%	if (RoleUtil.isTorihikisakiFurumai(role)
					        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
					        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
							<td nowrap <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
								<input type="hidden" name="tokaikyu_cd_<%=j%>" value="" />
								<input type="text" name="tokaikyu_na_<%=j%>" size="27" style="width: 145px;" value="" id="no_input_text" tabindex="-1" readonly/>
							</td>
				    <%	} else {	%>
							<td nowrap <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
								<input type="hidden" name="tokaikyu_cd_<%=j%>" value="" />
								<input type="text" name="tokaikyu_na_<%=j%>" size="16" style="width: 90px;" value="" id="no_input_text" tabindex="-1" readonly onChange="changeModifiedCondition()"/>
								<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"
									onClick="if(checkTorihikisaki()) pop_tokaiqSel('MainForm.tokaikyu_cd_<%=j%>','MainForm.tokaikyu_na_<%=j%>',MainForm.torihikisaki_cd.value);"/>
							</td>
					<%	} %>

					<%	if (RoleUtil.isTorihikisakiFurumai(role)
					        && (ButuryuKb.TC_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb())
					        	 || ButuryuKb.TYOKUNO_ICHIBA.getCode().equals(emergencyTorokuStatus.getButuryuKb()))) {	%>
							<td nowrap <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
								<input type="hidden" name="kikaku_cd_<%=j%>" value="" />
								<input type="text" name="kikaku_na_<%=j%>" size="27" style="width: 145px;" value="" id="no_input_text" tabindex="-1" readonly/>
							</td>
				    <%	} else {	%>
							<td nowrap <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
								<input type="hidden" name="kikaku_cd_<%=j%>" value="" />
								<input type="text" name="kikaku_na_<%=j%>" size="16" style="width: 90px;" value="" id="no_input_text" tabindex="-1" readonly onChange="changeModifiedCondition()"/>
								<img src="./images/b_select_w.png" width="40" height="20" align="absmiddle" alt="選択"
									onClick="if(checkTorihikisaki()) pop_kikakuSel('MainForm.kikaku_cd_<%=j%>','MainForm.kikaku_na_<%=j%>',MainForm.torihikisaki_cd.value);"/>
							</td>
					<%	} %>

			        <%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
			            <%	if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				                || emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    	        || emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    	        || emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
					                <td <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						            <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="STKClear(<%=j%>);"/>
					                </td>
                        <%	}	%>
		            <%	} else {	%>
					        <td <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						    <img src="./images/b_clear_w.png" width="40" height="20" align="absmiddle" alt="クリア" onClick="STKClear(<%=j%>);"/>
					        </td>
			        <%	} %>
		        </tr>
		      </table>
			</td>
		</tr>
		<%
		}
		%>

	</table>
	</div>

	<div align="center">
		<font color="#0000FF">
			※定貫商品は、入力された納品数を元に算出した納品数量で、登録されます。
		</font>
	</div>

</td>
</tr>
</table>

	<br>
	<table align="center" width="750" border="0" cellspacing="1" cellpadding="0" class="data" style="line-height: 22px;">
		<tr>
			<th nowrap  align="center" width="100">納品数合計</th>
			<td width="150" >
				<input type="text" name="hachu_kei_qt" size="10" style="width: 60px;" value="<%= HTMLUtil.toText(hachuKeiQt.longValue(), "#,##0") %>" class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
			<th nowrap  align="center" width="100">納品数量合計</th>
			<td width="150" >
				<input type="text" name="hachu_suryo_kei_qt" size="15" style="width: 85px;" value="<%= HTMLUtil.toText(hachuSuryoKeiQt.doubleValue(), "#,##0.##") %>" class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
			<th nowrap  align="center" width="100">原価金額合計</th>
			<td width="150" >
				<input type="text" name="genka_kei_qt" size="15" style="width: 85px;" value="<%= HTMLUtil.toText(genkaKeiQt.longValue(), "###,###,##0") %>"	class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
			<%// 取引先は非表示
			if (!RoleUtil.isTorihikisakiFurumai(role)) { %>
			<th nowrap  align="center" width="100">出庫金額合計</th>
			<td width="150" >
				<input type="text" name="ido_genka_kei_qt" size="15" style="width: 85px;" value="<%= HTMLUtil.toText(idoGenkaKeiQt.longValue(), "###,###,##0") %>"	class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
			<%} %>
			<th nowrap  align="center" width="100">売価金額合計</th>
			<td width="150" >
				<input type="text" name="baika_kei_qt" size="15" style="width: 85px;" value="<%= HTMLUtil.toText(baikaKeiQt.longValue(), "###,###,##0") %>" class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
		</tr>
	</table>

<%
}else{
//参照明細
%>
	<br />



<table>
<tr align = "left">
<td>

	<div align="left" style="overflow-x:hidden; width: 980px; height: 530px;">
	<table border="0" cellspacing="1" cellpadding="0" class="data" style="line-height: 14px;">
	    <tr>
			<th nowrap width="20" rowspan="2">No</th>
			<th nowrap width="100" align="center" >商品コード</th>
			<th nowrap width="324" align="center">商品名</th>
			<th nowrap width="41" align="center">入数</th>
			<th nowrap width="68" align="center" rowspan="2" >発注単位</th>
			<th nowrap width="55" align="center" rowspan="2" >納品数</th>
			<th nowrap width="80" align="center" rowspan="2" >納品数量</th>
			<th nowrap width="80" align="center" rowspan="2">原単価</th>
			<%// 取引先は非表示
			if (RoleUtil.isTorihikisakiFurumai(role)) { %>
				<th nowrap width="80" align="center" rowspan="2"></th>
			<%} else { %>
				<th nowrap width="80" align="center" rowspan="2">出庫単価</th>
			<%} %>
			<th nowrap width="70" align="center" rowspan="2">売単価</th>
	    </tr>
		<tr>
			<th nowrap colspan="3">
		      <table border="0" cellspacing="1" cellpadding="0" class="data" width="100%">
		        <tr>
		          <th align="center">産地</th>
		          <th align="center">等級</th>
		          <th align="center">規格</th>
		        </tr>
		      </table>
		    </th>
		</tr>
	<%
		DtHachuMeisaiBean dtHachuMeisaiBean = null;

		BigDecimal hachuKeiQt 		= new BigDecimal("0");
		BigDecimal hachuSuryoKeiQt 	= new BigDecimal("0");
		BigDecimal genkaKeiQt 		= new BigDecimal("0");
		BigDecimal idoGenkaKeiQt 	= new BigDecimal("0");
		BigDecimal baikaKeiQt 		= new BigDecimal("0");

		//対象行分出力
		for(int i = 0; i < dtHachuMeisaiList.size(); i++){
			dtHachuMeisaiBean = (DtHachuMeisaiBean)dtHachuMeisaiList.get(i);
			hachuKeiQt 		= hachuKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getHachuQtString()));
			hachuSuryoKeiQt = hachuSuryoKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getHachuSuryoQtString()));
			genkaKeiQt 		= genkaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getGenkaVlString()));
			idoGenkaKeiQt 	= idoGenkaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getIdoGenkaVlString()));
			baikaKeiQt		= baikaKeiQt.add(new BigDecimal(dtHachuMeisaiBean.getBaikaVlString()));
	%>

		<tr height="25">
			<td nowrap width="20" height="40" align="center" rowspan="2" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<%=i+1%>
			</td>

			<%-- 商品コード --%>
			<td nowrap  width="100" align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="hidden" name="syohin_cd_<%=i%>" value="<% if(dtHachuMeisaiBean.getSyohinCd() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSyohinCd()));%>">
				<input type="text" name="hachu_syohin_cd_<%=i%>" maxlength="13" size="16" value="<% if(dtHachuMeisaiBean.getHachuSyohinCd() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getHachuSyohinCd()));%>" id="no_input_text" style="ime-mode:disabled; width: 90px;" tabindex="-1" readonly>
			</td>

			<%-- 商品名 --%>
			<td nowrap  width="320" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="syohin_na_<%=i%>" size="30" style="width: 160px;" value="<% if(dtHachuMeisaiBean.getSyohinNa() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSyohinNa()));%>" id="no_input_text" tabindex="-1" readonly/>
				<input type="text" name="hi_kikaku_na_<%=i%>" size="26" style="width: 140px;" value="<% if(dtHachuMeisaiBean.getHiKikakuNa() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getHiKikakuNa()));%>" id="no_input_text" tabindex="-1" readonly/>
			</td>

			<%-- 入数 --%>
			<td nowrap width="45" align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="irisu_qt_<%=i%>" maxlength="4" size="4" style="width: 30px;" value="<%= HTMLUtil.toLabel(dtHachuMeisaiBean.getIrisuQtString(), "###0") %>" id="no_input_text"  class="numeric" tabIndex="-1" readonly/>
			</td>

			<%-- 発注単位 --%>
			<td nowrap rowspan="2" width="68" align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<select name="hachu_tani_cd_<%=i%>" size="1" style="width: 60px;" disabled >
					<option value=""></option>
				<%
				   if(nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
					List selectList = nohinTaniList.getBeanList();
					  for(int k = 0;k < selectList.size(); k++ ) {
					  	MaHachuTaniBean bean = (MaHachuTaniBean)selectList.get(k);
					  	String hachu_tani_cd = bean.getHachuTaniCd();
					  	String selected = "";

					  	if(hachu_tani_cd != null && dtHachuMeisaiBean.getHachuTaniCd().equals(hachu_tani_cd)){
					  	   selected = "selected";
					  	}
				%>
						<option value="<%=hachu_tani_cd%>"<%=selected%>><%=bean.getHachuTaniNa()%></option>
				<%
					  }
				   }
				%>
				</select>
			</td>

			<%-- 納品数 --%>
			<td nowrap rowspan="2" width="55"  align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="hachu_qt_<%=i%>" maxlength="4" size="6" value="<%= HTMLUtil.toLabel(dtHachuMeisaiBean.getHachuQtString(), "##0") %>" class="numeric" id="no_input_text" style="ime-mode:disabled; width: 40px;" tabindex="-1" readonly>
			</td>

			<%-- 納品数量 --%>
			<td nowrap rowspan="2" width="80"  align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="hachu_suryo_qt_<%=i%>" maxlength="9" size="11" value="<%= HTMLUtil.toLabel(dtHachuMeisaiBean.getHachuSuryoQtString(), "####0.##") %>"  class="numeric" style="ime-mode:disabled; width: 65px;" tabIndex="-1" id="no_input_text" readonly>
			</td>

			<%-- 原単価 --%>
			<td nowrap rowspan="2" width="80"  align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="gentanka_vl_<%=i%>" maxlength="10" size="11" value="<%= HTMLUtil.toLabel(dtHachuMeisaiBean.getKakuGentankaVlString(), "######0.00") %>" class="numeric" style="ime-mode:disabled; width: 65px;" id="no_input_text" tabindex="-1" readonly/>
			</td>

			<%-- 出庫単価 --%>
			<td nowrap rowspan="2" width="80"  align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<%
				// 取引先は非表示
				if(RoleUtil.isTorihikisakiFurumai(role)){
				%>
					<input type="hidden" name="shukatanka_vl_<%=i%>" value="<%= HTMLUtil.toLabel(dtHachuMeisaiBean.getIdoGentankaVlString(), "######0.00") %>" tabindex="-1" disabled readonly>
		    	<%}else{%>
					<input type="text" name="shukatanka_vl_<%=i%>" maxlength="10" size="11" value="<%= HTMLUtil.toLabel(dtHachuMeisaiBean.getIdoGentankaVlString(), "######0.00") %>" class="numeric" style="ime-mode:disabled; width: 65px;" id="no_input_text" tabindex="-1" readOnly/>
				<%}%>
			</td>

			<%-- 売単価 --%>
			<td nowrap rowspan="2" width="70"  align="center" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="baitanka_vl_<%=i%>" maxlength="7" size="9" value="<% if(dtHachuMeisaiBean.getKakuBaitankaVl() != 0 ) out.print(HTMLUtil.toLabel(dtHachuMeisaiBean.getKakuBaitankaVlString(), "#######0")); %>" class="numeric" style="ime-mode:disabled; width: 55px;" id="no_input_text" tabindex="-1" readonly/>
			</td>
		</tr>
		<tr style="height: 28px;">
			<td nowrap colspan="3" <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
		      <table border="0" width="100%">
		        <tr>

					<%-- 産地 --%>
					<td nowrap <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="santi_cd_<%=i%>" value="<% if(dtHachuMeisaiBean.getSantiCd() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSantiCd()));%>" />
						<input type="text" name="santi_na_<%=i%>" size="27" style="width: 145px;" value="<% if(dtHachuMeisaiBean.getSantiNa() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getSantiNa()));%>" id="no_input_text" tabindex="-1" readonly/>
					</td>

					<%-- 等級 --%>
					<td nowrap <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="tokaikyu_cd_<%=i%>" value="<% if(dtHachuMeisaiBean.getTokaikyuCd() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getTokaikyuCd()));%>" />
						<input type="text" name="tokaikyu_na_<%=i%>" size="27" style="width: 145px;" value="<% if(dtHachuMeisaiBean.getTokaikyuNa() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getTokaikyuNa()));%>" id="no_input_text" tabindex="-1" readonly/>
					</td>

					<%-- 規格 --%>
					<td nowrap <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="kikaku_cd_<%=i%>" value="<% if(dtHachuMeisaiBean.getKikakuCd() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getKikakuCd()));%>" />
						<input type="text" name="kikaku_na_<%=i%>" size="27" style="width: 145px;" value="<% if(dtHachuMeisaiBean.getKikakuNa() != null ) out.print(HTMLUtil.toText(dtHachuMeisaiBean.getKikakuNa()));%>" id="no_input_text" tabindex="-1" readonly/>
					</td>
					<td <% if(i%2==0){%> style="background-color: #FFFFCF" <%}%>><br>
					</td>
		        </tr>
		      </table>
			</td>
		</tr>
	<%
		}
		//空入力行の出力
		for ( int j = dtHachuMeisaiList.size(); j < 9; j++ ) {
	%>
		<tr height="25">

			<td nowrap width="20" height="40" align="center" rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<%=j+1%>
			</td>

			<%-- 商品コード --%>
			<td nowrap width="100" align="center" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="hidden" name="syohin_cd_<%=j%>" value="">
				<input type="text" name="hachu_syohin_cd_<%=j%>" maxlength="13" size="16" value="" style="ime-mode:disabled; width: 90px;" id="no_input_text" tabindex="-1" readonly>
			</td>

			<%-- 商品名 --%>
			<td nowrap width="320" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="syohin_na_<%=j%>" size="30" style="width: 160px;" value="" id="no_input_text" tabindex="-1" readonly/>
				<input type="text" name="hi_kikaku_na_<%=j%>" size="26" style="width: 140px;" value="" id="no_input_text" tabindex="-1" readonly/>
			</td>

			<%-- 入数 --%>
			<td nowrap width="45" align="center" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="irisu_qt_<%=j%>" maxlength="4" size="4" style="width: 30px;" value="" id="no_input_text"  class="numeric" tabIndex="-1" readonly>
			</td>

			<%-- 発注単位 --%>
			<td nowrap rowspan="2" width="68" align="center" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<select name="hachu_tani_cd_<%=j%>" size="1" style="width: 60px;" disabled>
					<option value=""></option>
				<%
				   if(nohinTaniList != null && nohinTaniList.getBeanList() != null && nohinTaniList.getBeanList().size() > 0) {
					List selectList = nohinTaniList.getBeanList();
					  for(int l = 0;l < selectList.size(); l ++ ) {
					  	MaHachuTaniBean bean = (MaHachuTaniBean)selectList.get(l);
					  	String hachu_tani_cd = bean.getHachuTaniCd();
					  	String selected = "";

					  	if(searchData.getParameter("hachu_tani_cd") != null && hachu_tani_cd != null && dtHachuMeisaiBean.getHachuTaniCd().equals(hachu_tani_cd)){
					  	   selected = "selected";
					  	}
				%>
						<option value="<%=hachu_tani_cd%>"<%=selected%>><%=bean.getHachuTaniNa()%>
						</option>
				<%
					  }
				   }

				%>
				</select>

			</td>

			<%-- 納品数 --%>
			<td nowrap width="55"  align="center" rowspan="2"  <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="hachu_qt_<%=j%>" maxlength="4" size="6" value="" class="numeric" style="ime-mode:disabled; width: 40px;" id="no_input_text" tabindex="-1" readonly/>
			</td>

			<%-- 納品数量 --%>
			<td nowrap width="80"  align="center" rowspan="2"  <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="hachu_suryo_qt_<%=j%>" maxlength="9" size="11" value=""  class="numeric" style="ime-mode:disabled; width: 65px;" id="no_input_text" tabindex="-1" readonly/>
			</td>

			<%-- 原単価 --%>
			<td nowrap width="80"  align="center" rowspan="2"  <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="gentanka_vl_<%=j%>" maxlength="10" size="11" value="" class="numeric" style="ime-mode:disabled; width: 65px;" id="no_input_text" tabindex="-1" readonly/>
			</td>

			<%-- 出庫単価 --%>
			<td nowrap width="80"  align="center" rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<%
				// 取引先は非表示
				if(RoleUtil.isTorihikisakiFurumai(role)){
				%>
					<input type="hidden" name="shukatanka_vl_<%=j%>" value="" tabindex="-1" disabled readonly>
		    	<%}else{%>
					<input type="text" name="shukatanka_vl_<%=j%>" maxlength="10" size="11" value="" class="numeric" style="ime-mode:disabled; width: 65px;" id="no_input_text" tabindex="-1" readonly/>
				<%}%>
			</td>

			<%-- 売単価 --%>
			<td nowrap width="70"  align="center" rowspan="2" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
				<input type="text" name="baitanka_vl_<%=j%>" maxlength="7" size="9" value="" class="numeric" style="ime-mode:disabled; width: 55px;" id="no_input_text" tabindex="-1" readonly/>
			</td>
		</tr>
		<tr style="height:28px;">
			<td nowrap width="45" colspan="3" <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
		      <table border="0" width="100%">
		        <tr>

					<%-- 産地 --%>
					<td nowrap <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="santi_cd_<%=j%>" value="" />
						<input type="text" name="santi_na_<%=j%>" size="27" style="width: 145px;" value="" id="no_input_text" tabindex="-1" readonly/>
					</td>

					<%-- 等級 --%>
					<td nowrap <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="tokaikyu_cd_<%=j%>" value="" />
						<input type="text" name="tokaikyu_na_<%=j%>" size="27" style="width: 145px;" value="" id="no_input_text" tabindex="-1" readonly/>
					</td>

					<%-- 規格 --%>
					<td nowrap <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>>
						<input type="hidden" name="kikaku_cd_<%=j%>" value="" />
						<input type="text" name="kikaku_na_<%=j%>" size="27" style="width: 145px;" value="" id="no_input_text" tabindex="-1" readonly/>
					</td>
					<td <% if(j%2==0){%> style="background-color: #FFFFCF" <%}%>><br>
					</td>
		        </tr>
		      </table>
			</td>
		</tr>

		<%
		}
		%>
	</table>
	</div>

	<div align="center">
		<font color="#0000FF">
			※定貫商品は、入力された納品数を元に算出した納品数量で、登録されます。
		</font>
	</div>

</td>
</tr>
</table>

	<br>

	<table align="center" width="750" border="0" cellspacing="1" cellpadding="0" class="data" style="line-height: 22px;">
		<tr>
			<th nowrap  align="center" width="100">納品数合計</th>
			<td width="150" >
				<input type="text" name="hachu_kei_qt" size="10" style="width: 60px;" value="<%= HTMLUtil.toText(hachuKeiQt.longValue(), "#,##0") %>" class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
			<th nowrap  align="center" width="100">納品数量合計</th>
			<td width="150" >
				<input type="text" name="hachu_suryo_kei_qt" size="15" style="width: 85px;" value="<%= HTMLUtil.toText(hachuSuryoKeiQt.doubleValue(), "#,##0.##") %>" class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
			<th nowrap  align="center" width="100">原価金額合計</th>
			<td width="150" >
				<input type="text" name="genka_kei_qt" size="15" style="width: 85px;" value="<%= HTMLUtil.toText(genkaKeiQt.longValue(), "###,###,##0") %>"	class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
			<%// 取引先は非表示
			if (!RoleUtil.isTorihikisakiFurumai(role)) { %>
			<th nowrap  align="center" width="100">出庫金額合計</th>
			<td width="150" >
				<input type="text" name="ido_genka_kei_qt" size="15" style="width: 85px;" value="<%= HTMLUtil.toText(idoGenkaKeiQt.longValue(), "###,###,##0") %>"	class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
			<%} %>
			<th nowrap  align="center" width="100">売価金額合計</th>
			<td width="150" >
				<input type="text" name="baika_kei_qt" size="15" style="width: 85px;" value="<%= HTMLUtil.toText(baikaKeiQt.longValue(), "###,###,##0") %>" class="numeric" id="no_input_text" tabindex="-1" readonly >
			</td>
		</tr>
	</table>

<%
}
%>


	<br>
	<table width="900" border="0" cellspacing="0" cellpadding="0" >
		<tr>
			<td align="left">
				<input type="button" value="&nbsp;緊急発注登録一覧&nbsp;" class="btn" onClick="doBackTran();" style="width: 167px;" />
			</td>
			<td align="right">
<% if(emergencyTorokuStatus.getDisplayMode().equals("Insert")) { %>
				<input type="button" value="&nbsp;登&emsp;録&nbsp;" style="width: 70px;"  name="" class="btn"  onClick="doInsertTran();">
				<%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
				    <%	if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				    		|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    		|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    		|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
						        <input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis%> style="width: 59px;">
				    <%	}	%>
				<%	} else {	%>
						<input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis%> style="width: 59px;">
				<%	} %>

<% }else if(emergencyTorokuStatus.getDisplayMode().equals("Update")) { %>
				<input type="button" value="&nbsp;更&emsp;新&nbsp;"  name="" class="btn"  onClick="doUpdateTran();">
				<%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
				    <%	if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				    		|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    		|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    		|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
				    			<input type="button" value="&nbsp;コピー&nbsp;"  name="" class="btn"  onClick="doCopyTran();" style="width: 56px;">
				    			<input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis%> style="width: 59px;">
				    <%	}	%>
				<%	} else {	%>
				        <input type="button" value="&nbsp;コピー&nbsp;"  name="" class="btn"  onClick="doCopyTran();" style="width: 56px;">
						<input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis%> style="width: 59px;">
				<%	} %>

<% }else if(emergencyTorokuStatus.getDisplayMode().equals("Reference")) { %>
				<%	if (RoleUtil.isTorihikisakiFurumai(role)) {	%>
				    <%	if (emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TYOKUNO.getCode())
				    		|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_IKKATU.getCode())
				    		|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.TC_TENBETSU.getCode())
				    		|| emergencyTorokuStatus.getButuryuKb().equals(ButuryuKb.RS.getCode())) {	%>
				    		    <input type="button" value="&nbsp;コピー&nbsp;"  name="" class="btn"  onClick="doCopyTran();" style="width: 56px;">
						        <input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis%> style="width: 59px;">
				    <%	}	%>
				<%	} else {	%>
				        <input type="button" value="&nbsp;コピー&nbsp;"  name="" class="btn"  onClick="doCopyTran();" style="width: 56px;">
						<input type="button" value="全クリア" name="" class="btn"  onClick="doAllCrearTran();" <%=buttonDis%> style="width: 59px;">
				<%	} %>

<% } %>
			</td>
	 </table>

</form>
</div>

<!-- Body END -->
		</td>
	</tr>
</table>
</body>